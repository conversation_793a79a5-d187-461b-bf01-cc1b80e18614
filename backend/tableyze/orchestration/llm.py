"""
LLM integration with strict JSON-only responses and schema validation.

This module provides a safe interface to LLM services that ensures:
- No raw data is sent to the LLM
- Responses are JSON-only and schema-validated
- Temperature is set to 0 for determinism
- Retry logic for schema violations
"""

import os
import json
import time
import requests
from typing import Type, Dict, Any
from pydantic import BaseModel, ValidationError
from .schemas import DatasetCard


def json_only(system_prompt: str, user_payload: dict, schema: Type[BaseModel]) -> dict:
    """
    Call LLM with temperature=0 and strict JSON-only response validation.
    
    Args:
        system_prompt: System prompt for the LLM
        user_payload: User data to send (should be sanitized summaries only)
        schema: Pydantic schema to validate response against
        
    Returns:
        Validated JSON response as dict
        
    Raises:
        ValueError: If LLM response cannot be validated after retries
    """
    # Get LLM configuration from environment
    llm_base_url = os.getenv("LLM_BASE_URL", "https://api.openai.com/v1")
    llm_api_key = os.getenv("LLM_API_KEY", "")
    
    # Prepare the request
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {llm_api_key}" if llm_api_key else ""
    }
    
    # Construct the full prompt with JSON-only instruction
    full_prompt = f"""{system_prompt}

IMPORTANT: You must respond with valid JSON ONLY. No markdown, no explanations, no additional text.
The response must be a valid JSON object that matches the required schema.

User data: {json.dumps(user_payload, indent=2)}

Respond with JSON ONLY:"""
    
    payload = {
        "model": "gpt-4o-mini",  # More capable model for better analysis
        "messages": [
            {"role": "system", "content": full_prompt}
        ],
        "temperature": 0.0,  # Deterministic responses
        "max_tokens": 2000,
        "stream": False
    }
    
    # Try up to 3 times with delays for rate limiting
    for attempt in range(3):
        try:
            response = requests.post(
                f"{llm_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            content = result["choices"][0]["message"]["content"].strip()
            
            # Parse JSON response
            try:
                json_response = json.loads(content)
            except json.JSONDecodeError as e:
                if attempt == 0:
                    # Retry with schema violation reminder
                    payload["messages"][0]["content"] = f"""{full_prompt}

SCHEMA VIOLATION: Your previous response was not valid JSON. Please respond with valid JSON ONLY that matches the required schema."""
                    continue
                else:
                    raise ValueError(f"LLM response is not valid JSON: {e}")
            
            # Validate against schema
            try:
                validated_response = schema(**json_response)
                return validated_response.model_dump()
            except ValidationError as e:
                if attempt == 0:
                    # Retry with schema violation reminder
                    payload["messages"][0]["content"] = f"""{full_prompt}

SCHEMA VIOLATION: Your previous response did not match the required schema. Please ensure your JSON response matches the exact schema requirements."""
                    continue
                else:
                    raise ValueError(f"LLM response does not match schema: {e}")
                    
        except requests.RequestException as e:
            if attempt == 2:  # Last attempt
                # Provide specific error messages for common issues
                if "429" in str(e):
                    raise ValueError(f"Rate limit exceeded. Please wait a moment and try again. Error: {e}")
                elif "401" in str(e):
                    raise ValueError(f"Invalid API key. Please check your LLM_API_KEY. Error: {e}")
                elif "403" in str(e):
                    raise ValueError(f"Access forbidden. Please check your API key permissions. Error: {e}")
                else:
                    raise ValueError(f"LLM request failed: {e}")
            else:
                # Add delay for rate limiting
                if "429" in str(e):
                    delay = (attempt + 1) * 2  # 2, 4, 6 seconds
                    print(f"Rate limited, waiting {delay} seconds before retry...")
                    time.sleep(delay)
                continue
    
    # If we get here, all attempts failed
    raise ValueError("LLM request failed after all retry attempts")


def create_analysis_summary_fallback(card: DatasetCard, warnings: list, correlations: list, duplicates: float) -> dict:
    """
    Create a fallback analysis summary when LLM is unavailable.
    
    Args:
        card: DatasetCard with dataset metadata
        warnings: List of warnings
        correlations: List of top correlations
        duplicates: Duplicate row rate
        
    Returns:
        Dict with analysis summary
    """
    summary_parts = []
    
    # Basic dataset info
    summary_parts.append(f"This dataset contains {card.n_rows:,} rows and {card.n_cols} columns.")
    
    # Missing data
    high_missing = [c for c in card.columns if c.missing_pct > 20]
    if high_missing:
        summary_parts.append(f"Columns with significant missing data (>20%): {', '.join([c.name for c in high_missing])}")
    
    # Warnings
    if warnings:
        high_severity = [w for w in warnings if w.get('severity') == 'high']
        if high_severity:
            summary_parts.append(f"High priority issues found: {len(high_severity)} data quality problems identified.")
    
    # Duplicates
    if duplicates > 0.1:
        summary_parts.append(f"Duplicate rows detected: {duplicates:.1%} of rows are duplicates.")
    
    # Correlations
    if correlations:
        top_corr = correlations[0] if correlations else None
        if top_corr and abs(top_corr.get('value', 0)) > 0.5:
            pair = top_corr.get('pair', [])
            value = top_corr.get('value', 0)
            if len(pair) >= 2:
                summary_parts.append(f"Strong correlation found between {pair[0]} and {pair[1]} ({value:.2f}).")
            else:
                summary_parts.append(f"Strong correlation found: {value:.2f}.")
    
    # PII
    if card.pii_flags:
        summary_parts.append(f"Potential PII columns identified: {', '.join(card.pii_flags)}")
    
    paragraph = " ".join(summary_parts) if summary_parts else "Dataset analysis completed with no significant issues identified."
    
    return {
        "paragraph": paragraph,
        "bullets": [
            f"Dataset size: {card.n_rows:,} rows × {card.n_cols} columns",
            f"Missing data: {len(high_missing)} columns with >20% missing",
            f"Data quality: {len(warnings)} issues identified",
            f"Duplicates: {duplicates:.1%} of rows"
        ],
        "callouts": [
            {"type": "dataset_size", "value": card.n_rows, "note": f"{card.n_rows:,} total rows"},
            {"type": "missing_data", "value": len(high_missing), "note": f"{len(high_missing)} columns with high missingness"},
            {"type": "duplicates", "value": duplicates, "note": f"{duplicates:.1%} duplicate rows"}
        ]
    }
