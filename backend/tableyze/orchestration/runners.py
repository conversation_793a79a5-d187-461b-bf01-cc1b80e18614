"""
Workflow runners for materializing artifacts and managing page views.

This module handles the execution of workflow graphs and the creation
of artifacts in the database, as well as providing page view functionality.
"""

import hashlib
import json
import time
from django.utils import timezone
from django.conf import settings
import os
from .schemas import PageView
from ..models import Analysis, LangArtifact
from .graphs import upload_graph, analysis_graph


# Mapping of pages to their workflow graphs
PAGE_TO_GRAPH = {
    "upload": upload_graph,
    "analysis": analysis_graph,
}


def _hash_payload(obj, session_id: str = None) -> str:
    """
    Generate a SHA256 hash of a JSON-serializable object.
    
    Args:
        obj: Object to hash
        session_id: Optional session ID to include in hash for uniqueness
        
    Returns:
        SHA256 hex string
    """
    if session_id:
        # Include session ID to ensure uniqueness across different analyses
        hash_input = {"session_id": session_id, "payload": obj}
    else:
        hash_input = obj
    
    j = json.dumps(hash_input, sort_keys=True, separators=(",", ":")).encode()
    return hashlib.sha256(j).hexdigest()


def _mark_downstream_stale(session: Analysis, artifact_id: str):
    """
    Mark downstream artifacts as stale when an upstream artifact changes.
    
    Args:
        session: AnalysisSession instance
        artifact_id: ID of the artifact that changed
    """
    # Find all artifacts that depend on this one
    # SQLite doesn't support contains lookup, so we'll check manually
    dependent_artifacts = LangArtifact.objects.filter(
        analysis=session,
        status="ready"
    )
    
    # Filter artifacts that depend on this one
    dependent_artifacts = [
        artifact for artifact in dependent_artifacts
        if artifact_id in (artifact.depends_on or [])
    ]
    
    # Mark them as stale
    for artifact in dependent_artifacts:
        artifact.status = "stale"
        artifact.save(update_fields=["status"])
        
        # Recursively mark their dependents as stale
        _mark_downstream_stale(session, artifact.id)


def resolve_dataset_path(dataset_id: str) -> str:
    """
    Resolve dataset ID to file path.
    
    Args:
        dataset_id: Dataset identifier (could be file path or ID)
        
    Returns:
        Absolute path to dataset file
    """
    # If it's already a path, return it
    if os.path.isabs(dataset_id) and os.path.exists(dataset_id):
        return dataset_id
    
    # Try to resolve from Django settings
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    
    # If it starts with media URL, convert to path
    if dataset_id.startswith(media_url):
        rel_path = dataset_id[len(media_url):]
        return os.path.join(media_root, rel_path)
    
    # Try as relative path from media root
    potential_path = os.path.join(media_root, dataset_id)
    if os.path.exists(potential_path):
        return potential_path
    
    # Fallback: assume it's already a valid path
    return dataset_id


def run_page_graph(session: Analysis, page: str, state: dict):
    """
    Run a page workflow graph and create artifacts.
    
    Args:
        session: Analysis instance
        page: Page identifier ("upload" or "analysis")
        state: Initial state for the workflow
        
    Returns:
        Created artifact(s) or None
    """
    if page not in PAGE_TO_GRAPH:
        raise ValueError(f"Unknown page: {page}")
    
    # Get the workflow graph
    app = PAGE_TO_GRAPH[page]()
    start_time = time.time()
    
    try:

        state["session_id"] = str(session.id)
        

        # For analysis page, use streaming to deliver intermediate results
        if page == "analysis":
            # Temporarily disable streaming for debugging
            # return _run_analysis_with_streaming(session, app, state, start_time)
            pass
        
        # For upload page, use regular invoke
        print(f"🚀 RUNNING {page.upper()} GRAPH for session {session.id}")
        result = app.invoke(state)
        print(f"✅ {page.upper()} GRAPH COMPLETED - Result keys: {list(result.keys())}")
        
        if page == "upload":
            # Create dataset_card artifact
            payload = result["dataset_card"].model_dump()
            artifact_id = _hash_payload(payload, str(session.id))
            
            # Check if artifact already exists
            print(f"📦 Creating DATASET_CARD artifact for upload...")
            try:
                artifact = LangArtifact.objects.get(id=artifact_id)
                # Update existing artifact
                artifact.status = "ready"
                artifact.payload = payload
                artifact.metrics = {"latency_sec": time.time() - start_time}
                artifact.analysis = session
                artifact.save()
                created = False
                print(f"🔄 Updated existing DATASET_CARD artifact: {artifact.id}")
            except LangArtifact.DoesNotExist:
                # Create new artifact
                artifact = LangArtifact.objects.create(
                    id=artifact_id,
                    analysis=session,
                    page=page,
                    kind="dataset_card",
                    status="ready",
                    depends_on=[],
                    payload=payload,
                    metrics={"latency_sec": time.time() - start_time}
                )
                created = True
                print(f"✨ Created new DATASET_CARD artifact: {artifact.id}")
            
            # Update session current pointers if supported by model
            if hasattr(session, "current"):
                try:
                    session.current = {**(getattr(session, "current") or {}), "dataset_card": artifact.id}
                    # Avoid referencing non-existent fields in update_fields
                    session.save()
                except Exception:
                    session.save()
            
            # Mark downstream artifacts as stale
            _mark_downstream_stale(session, artifact.id)
            
            return artifact
        
        elif page == "analysis":
            # Create analysis artifacts (temporarily using regular invoke for debugging)
            print(f"📊 Creating ANALYSIS artifacts...")
            artifacts = []
            
            # Create features artifact
            if "features" in result:
                print(f"📈 Creating FEATURES artifact...")
                features_payload = result["features"]
                features_id = _hash_payload(features_payload, str(session.id))
                try:
                    artifact = LangArtifact.objects.get(id=features_id)
                    artifact.status = "ready"
                    artifact.payload = features_payload
                    artifact.save()
                    print(f"🔄 Updated existing FEATURES artifact: {artifact.id}")
                except LangArtifact.DoesNotExist:
                    artifact = LangArtifact.objects.create(
                        id=features_id,
                        analysis=session,
                        page="analysis",
                        kind="features",
                        status="ready",
                        depends_on=[],
                        payload=features_payload,
                        metrics={"latency_sec": time.time() - start_time}
                    )
                    print(f"✨ Created new FEATURES artifact: {artifact.id}")
                artifacts.append(artifact)
            
            # Create correlations artifact (top 5)
            if "correlations" in result:
                print(f"🔗 Creating CORRELATIONS artifact...")
                correlations_payload = result["correlations"]
                correlations_id = _hash_payload(correlations_payload, str(session.id))
                try:
                    artifact = LangArtifact.objects.get(id=correlations_id)
                    artifact.status = "ready"
                    artifact.payload = correlations_payload
                    artifact.save()
                    print(f"🔄 Updated existing CORRELATIONS artifact: {artifact.id}")
                except LangArtifact.DoesNotExist:
                    artifact = LangArtifact.objects.create(
                        id=correlations_id,
                        analysis=session,
                        page="analysis",
                        kind="correlations",
                        status="ready",
                        depends_on=[],
                        payload=correlations_payload,
                        metrics={"latency_sec": time.time() - start_time}
                    )
                    print(f"✨ Created new CORRELATIONS artifact: {artifact.id}")
                artifacts.append(artifact)
            
            # Create mic_pairs artifact (all correlations)
            if "mic_pairs" in result:
                print(f"🔗 Creating MIC_PAIRS artifact (all correlations)...")
                mic_pairs_raw = result["mic_pairs"]
                # Convert tuple keys to JSON-serializable format: {(col1, col2): value} -> {"col1,col2": value}
                mic_pairs_payload = {}
                for (col1, col2), value in mic_pairs_raw.items():
                    mic_pairs_payload[f"{col1},{col2}"] = float(value)
                
                mic_pairs_id = _hash_payload(str(mic_pairs_payload), str(session.id))
                try:
                    artifact = LangArtifact.objects.get(id=mic_pairs_id)
                    artifact.status = "ready"
                    artifact.payload = mic_pairs_payload
                    artifact.save()
                    print(f"🔄 Updated existing MIC_PAIRS artifact: {artifact.id}")
                except LangArtifact.DoesNotExist:
                    artifact = LangArtifact.objects.create(
                        id=mic_pairs_id,
                        analysis=session,
                        page="analysis",
                        kind="mic_pairs",
                        status="ready",
                        depends_on=[],
                        payload=mic_pairs_payload,
                        metrics={"latency_sec": time.time() - start_time}
                    )
                    print(f"✨ Created new MIC_PAIRS artifact: {artifact.id}")
                artifacts.append(artifact)
            
            # Create warnings artifact
            if "warnings" in result:
                print(f"⚠️ Creating WARNINGS artifact...")
                warnings_payload = result["warnings"].model_dump() if hasattr(result["warnings"], "model_dump") else result["warnings"]
                warnings_id = _hash_payload(warnings_payload, str(session.id))
                try:
                    artifact = LangArtifact.objects.get(id=warnings_id)
                    artifact.status = "ready"
                    artifact.payload = warnings_payload
                    artifact.save()
                    print(f"🔄 Updated existing WARNINGS artifact: {artifact.id}")
                except LangArtifact.DoesNotExist:
                    artifact = LangArtifact.objects.create(
                        id=warnings_id,
                        analysis=session,
                        page="analysis",
                        kind="warnings",
                        status="ready",
                        depends_on=[],
                        payload=warnings_payload,
                        metrics={"latency_sec": time.time() - start_time}
                    )
                    print(f"✨ Created new WARNINGS artifact: {artifact.id}")
                artifacts.append(artifact)
            
            # Create summary artifact
            if "analysis_summary" in result:
                print(f"📝 Creating ANALYSIS_SUMMARY artifact...")
                summary_payload = result["analysis_summary"]
                summary_id = _hash_payload(summary_payload, str(session.id))
                try:
                    artifact = LangArtifact.objects.get(id=summary_id)
                    artifact.status = "ready"
                    artifact.payload = summary_payload
                    artifact.save()
                    print(f"🔄 Updated existing ANALYSIS_SUMMARY artifact: {artifact.id}")
                except LangArtifact.DoesNotExist:
                    artifact = LangArtifact.objects.create(
                        id=summary_id,
                        analysis=session,
                        page="analysis",
                        kind="analysis_summary",
                        status="ready",
                        depends_on=[],
                        payload=summary_payload,
                        metrics={"latency_sec": time.time() - start_time}
                    )
                    print(f"✨ Created new ANALYSIS_SUMMARY artifact: {artifact.id}")
                artifacts.append(artifact)
            
            print(f"🎯 ANALYSIS COMPLETE - Created {len(artifacts)} artifacts: {[a.kind for a in artifacts]}")
            return artifacts
    
    except Exception as e:
        # Create error artifact with more detailed error info
        import traceback
        error_details = {
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc()
        }
        print(f"ERROR in run_page_graph: {e}")
        print(f"ERROR traceback: {traceback.format_exc()}")
        
        error_artifact = LangArtifact.objects.create(
            id=f"error_{int(time.time())}",
            analysis=session,
            page=page,
            kind="error",
            status="error",
            depends_on=[],
            payload=error_details,
            metrics={"latency_sec": time.time() - start_time}
        )
        raise e


def view_page(session: Analysis, page: str) -> PageView:
    """
    Get the current view of a page with all ready artifacts.
    
    Args:
        session: Analysis instance
        page: Page identifier
        
    Returns:
        PageView with artifacts and status
    """
    # Get all ready artifacts for this page
    artifacts_qs = LangArtifact.objects.filter(
        analysis=session,
        page=page,
        status="ready"
    ).order_by("-created_at")
    
    # Group by kind and take the latest of each
    artifacts = {}
    for artifact in artifacts_qs:
        if artifact.kind not in artifacts:  # Latest per kind
            artifacts[artifact.kind] = {
                "artifact_id": artifact.id,
                "payload": artifact.payload
            }
    
    # Check for stale artifacts
    stale = False
    
    # Check if any dependencies are stale
    for artifact in artifacts_qs:
        if artifact.depends_on:
            # Check if any dependencies are newer than this artifact
            dep_artifacts = LangArtifact.objects.filter(
                id__in=artifact.depends_on,
                analysis=session
            )
            for dep in dep_artifacts:
                if dep.created_at > artifact.created_at:
                    stale = True
                    break
        if stale:
            break
    
    # Check for explicit stale status
    stale_artifacts = LangArtifact.objects.filter(
        analysis=session,
        page=page,
        status="stale"
    )
    if stale_artifacts.exists():
        stale = True
    
    page_view = PageView(
        artifacts=artifacts,
        stale=stale,
        message="Stale - Recompute" if stale else None
    )
    
    # Convert to dictionary for Django serialization
    return page_view.model_dump()


def _run_analysis_with_streaming(session: Analysis, app, state: dict, start_time: float):
    """
    Run analysis workflow with streaming intermediate results.
    
    This function creates artifacts as soon as each node completes,
    allowing the frontend to receive intermediate results while the
    workflow continues running.
    
    Args:
        session: Analysis instance
        app: Compiled analysis graph
        state: Initial state for the workflow
        start_time: Start time for metrics
        
    Returns:
        List of all created artifacts
    """
    print(f"Running analysis with streaming for session {session.id}")
    dataset_card_id = (session.current or {}).get("dataset_card")
    
    artifacts = []
    
    try:
        # Use astream to get intermediate results as each node completes
        async def stream_workflow():
            async for chunk in app.astream(state):
                for node_name, node_result in chunk.items():
                    print(f"Node {node_name} completed")
                    
                    if node_name == "analyze_dataset_features":
                        # Create features artifact immediately (fast)
                        features_artifact = _create_features_artifact(
                            session, node_result, dataset_card_id, start_time
                        )
                        artifacts.append(features_artifact)
                        
                        print(f"Delivered features artifact")
                        
                    elif node_name == "analyze_dataset_correlations":
                        # Create correlations artifact (slow)
                        correlations_artifact = _create_correlations_artifact(
                            session, node_result, dataset_card_id, start_time
                        )
                        artifacts.append(correlations_artifact)
                        
                        print(f"Delivered correlations artifact")
                        
                    elif node_name == "compute_warnings":
                        # Create warnings artifact immediately
                        warnings_artifact = _create_warnings_artifact(
                            session, node_result, dataset_card_id, start_time
                        )
                        artifacts.append(warnings_artifact)
                        
                        print(f"Delivered warnings artifact")
                        
                    elif node_name == "llm_analysis_summary":
                        # Create final summary artifact
                        summary_artifact = _create_summary_artifact(
                            session, node_result, artifacts, start_time
                        )
                        artifacts.append(summary_artifact)
                        
                        print(f"Delivered analysis summary artifact")
                        
        # Run the async workflow
        import asyncio
        asyncio.run(stream_workflow())
        
        # Update session with all artifact references
        if hasattr(session, "current") and artifacts:
            try:
                session.current = {
                    **(getattr(session, "current") or {}),
                    **{artifact.kind: artifact.id for artifact in artifacts}
                }
                session.save()
            except Exception:
                session.save()
        
        return artifacts
        
    except Exception as e:
        print(f"Error in streaming analysis: {e}")
        # Create error artifact
        error_artifact = LangArtifact.objects.create(
            id=f"error_{int(time.time())}",
            analysis=session,
            page="analysis",
            kind="error",
            status="error",
            depends_on=[],
            payload={"error": str(e)},
            metrics={"latency_sec": time.time() - start_time}
        )
        raise e


def _create_features_artifact(session: Analysis, node_result: dict, dataset_card_id: str, start_time: float):
    """Create and save features artifact."""
    features_payload = node_result["features"]
    features_id = _hash_payload(features_payload, str(session.id))
    
    try:
        artifact = LangArtifact.objects.get(id=features_id)
        # Update existing artifact
        artifact.status = "ready"
        artifact.payload = features_payload
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.analysis = session
        artifact.save()
        created = False
    except LangArtifact.DoesNotExist:
        # Create new artifact
        artifact = LangArtifact.objects.create(
            id=features_id,
            analysis=session,
            page="analysis",
            kind="features",
            status="ready",
            depends_on=[dataset_card_id] if dataset_card_id else [],
            payload=features_payload,
            metrics={"latency_sec": time.time() - start_time}
        )
        created = True
    
    if not created:
        artifact.analysis = session
        artifact.page = "analysis"
        artifact.status = "ready"
        artifact.depends_on = [dataset_card_id] if dataset_card_id else []
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.save()
    
    return artifact


def _create_correlations_artifact(session: Analysis, node_result: dict, dataset_card_id: str, start_time: float):
    """Create and save correlations artifact."""
    correlations_payload = node_result["correlations"]
    correlations_id = _hash_payload(correlations_payload, str(session.id))
    
    try:
        artifact = LangArtifact.objects.get(id=correlations_id)
        # Update existing artifact
        artifact.status = "ready"
        artifact.payload = correlations_payload
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.analysis = session
        artifact.save()
        created = False
    except LangArtifact.DoesNotExist:
        # Create new artifact
        artifact = LangArtifact.objects.create(
            id=correlations_id,
            analysis=session,
            page="analysis",
            kind="correlations",
            status="ready",
            depends_on=[dataset_card_id] if dataset_card_id else [],
            payload=correlations_payload,
            metrics={"latency_sec": time.time() - start_time}
        )
        created = True
    
    if not created:
        artifact.analysis = session
        artifact.page = "analysis"
        artifact.status = "ready"
        artifact.depends_on = [dataset_card_id] if dataset_card_id else []
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.save()
    
    return artifact


def _create_warnings_artifact(session: Analysis, node_result: dict, dataset_card_id: str, start_time: float):
    """Create and save warnings artifact."""
    warnings_payload = node_result["warnings"].model_dump()
    warnings_id = _hash_payload(warnings_payload, str(session.id))
    
    try:
        artifact = LangArtifact.objects.get(id=warnings_id)
        # Update existing artifact
        artifact.status = "ready"
        artifact.payload = warnings_payload
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.analysis = session
        artifact.save()
        created = False
    except LangArtifact.DoesNotExist:
        # Create new artifact
        artifact = LangArtifact.objects.create(
            id=warnings_id,
            analysis=session,
            page="analysis",
            kind="warnings",
            status="ready",
            depends_on=[dataset_card_id] if dataset_card_id else [],
            payload=warnings_payload,
            metrics={"latency_sec": time.time() - start_time}
        )
        created = True
    
    if not created:
        artifact.analysis = session
        artifact.page = "analysis"
        artifact.status = "ready"
        artifact.depends_on = [dataset_card_id] if dataset_card_id else []
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.save()
    
    return artifact


def _create_summary_artifact(session: Analysis, node_result: dict, existing_artifacts: list, start_time: float):
    """Create and save analysis summary artifact."""
    summary_payload = node_result["analysis_summary"]
    summary_id = _hash_payload(summary_payload, str(session.id))
    
    # Find warnings and correlations artifacts for dependencies
    warnings_artifact = next((a for a in existing_artifacts if a.kind == "warnings"), None)
    correlations_artifact = next((a for a in existing_artifacts if a.kind == "correlations"), None)
    
    depends_on = []
    if warnings_artifact:
        depends_on.append(warnings_artifact.id)
    if correlations_artifact:
        depends_on.append(correlations_artifact.id)
    
    try:
        artifact = LangArtifact.objects.get(id=summary_id)
        # Update existing artifact
        artifact.status = "ready"
        artifact.payload = summary_payload
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.analysis = session
        artifact.depends_on = depends_on
        artifact.save()
        created = False
    except LangArtifact.DoesNotExist:
        # Create new artifact
        artifact = LangArtifact.objects.create(
            id=summary_id,
            analysis=session,
            page="analysis",
            kind="analysis_summary",
            status="ready",
            depends_on=depends_on,
            payload=summary_payload,
            metrics={"latency_sec": time.time() - start_time}
        )
        created = True
    
    if not created:
        artifact.analysis = session
        artifact.page = "analysis"
        artifact.status = "ready"
        artifact.depends_on = depends_on
        artifact.metrics = {"latency_sec": time.time() - start_time}
        artifact.save()
    
    return artifact
