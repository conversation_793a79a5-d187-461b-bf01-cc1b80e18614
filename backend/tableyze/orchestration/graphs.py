"""
LangGraph-based workflow orchestration for page-centric analysis.

This module defines the workflow graphs for different analysis pages,
including the upload and analysis workflows with LLM integration.
"""

from langgraph.graph import StateGraph, END
from typing import Dict, Any, List
import pandas as pd
from .schemas import DatasetCard, Warnings, Correlations, AnalysisSummary
from . import tools, llm


def upload_graph():
    """
    Create the upload workflow graph.
    
    This graph:
    1. Profiles the dataset to create a DatasetCard
    2. Stores the result as a dataset_card artifact
    
    Returns:
        Compiled StateGraph for upload workflow
    """
    def profile_dataset(state: Dict[str, Any]):
        """Profile the dataset and create a dataset card."""
        state["dataset_card"] = tools.build_dataset_card(
            state["dataset_path"], 
            state["dataset_id"],
            state.get("user_context", "")
        )
        return state
    
    # Create the graph
    g = StateGraph(dict)
    g.add_node("profile_dataset", profile_dataset)
    g.set_entry_point("profile_dataset")
    g.add_edge("profile_dataset", END)
    
    return g.compile()


def analysis_graph():
    """
    Create the analysis workflow graph.
    
    This graph:
    1. Builds the dataset card
    2. Computes structured statistics (warnings, correlations, duplicates, imbalances)
    3. Generates LLM analysis summary
    
    Returns:
        Compiled StateGraph for analysis workflow
    """
    def load_dataset_card(state: Dict[str, Any]):
        """Load the dataset card from the session."""
        print(f"🔄 LOAD_DATASET_CARD: Starting...")
        # The dataset card should already be in the session from the upload workflow
        # We need to load it from the session's current artifacts
        from ..models import Analysis, LangArtifact
        
        session_id = state.get("session_id")
        if session_id:
            try:
                session = Analysis.objects.get(id=session_id)
                dataset_card_id = (session.current or {}).get("dataset_card")
                print(f"🔍 LOAD_DATASET_CARD: Session {session_id}, dataset_card_id: {dataset_card_id}")
                if dataset_card_id:
                    artifact = LangArtifact.objects.get(id=dataset_card_id)
                    print(f"📦 LOAD_DATASET_CARD: Found artifact: {artifact.id}")
                    # Convert the payload back to DatasetCard object
                    from ..orchestration.schemas import DatasetCard
                    state["dataset_card"] = DatasetCard(**artifact.payload)
                    print(f"✅ LOAD_DATASET_CARD: Loaded from existing artifact")
                else:
                    # Fallback: build it
                    print(f"🆕 LOAD_DATASET_CARD: No dataset_card_id, building new...")
                    state["dataset_card"] = tools.build_dataset_card(
                        state["dataset_path"], 
                        state["dataset_id"],
                        state.get("user_context", "")
                    )
                    print(f"✅ LOAD_DATASET_CARD: Built new dataset card")
            except Exception as e:
                print(f"❌ LOAD_DATASET_CARD: Failed to load from session: {e}")
                # Fallback: build it
                state["dataset_card"] = tools.build_dataset_card(
                    state["dataset_path"], 
                    state["dataset_id"],
                    state.get("user_context", "")
                )
                print(f"🔄 LOAD_DATASET_CARD: Built fallback dataset card")
        else:
            # Fallback: build it
            state["dataset_card"] = tools.build_dataset_card(
                state["dataset_path"], 
                state["dataset_id"],
                state.get("user_context", "")
            )
        return state
    
    def analyze_dataset_features(state: Dict[str, Any]):
        """Fast dataset feature analysis - statistics and histograms only"""
        print(f"📊 ANALYZE_DATASET_FEATURES: Starting...")
        dataset_path = state["dataset_path"]
        
        # Load user drops if available
        user_drops = tools.load_user_drops(dataset_path)
        print(f"🔧 ANALYZE_DATASET_FEATURES: User drops: {user_drops}")
        
        # Perform fast feature analysis (no correlations)
        print(f"⚡ ANALYZE_DATASET_FEATURES: Computing features...")
        analysis_results = tools.analyze_dataset_features(dataset_path, user_drops)
        print(f"✅ ANALYZE_DATASET_FEATURES: Computed {len(analysis_results.get('features', []))} features")
        
        # Store results in state
        state["features"] = analysis_results["features"]
        state["duplicates"] = analysis_results["duplicates"]
        state["imbalances"] = analysis_results["imbalances"]
        state["dropped_columns"] = analysis_results["dropped_columns"]
        state["summary"] = analysis_results["summary"]
        state["df_clean"] = analysis_results["df_clean"]  # Store cleaned DataFrame for warnings
        state["numerical_cols"] = analysis_results["numerical_cols"]
        state["categorical_cols"] = analysis_results["categorical_cols"]
        
        print(f"📈 ANALYZE_DATASET_FEATURES: Complete - {len(state['features'])} features analyzed")
        return state
    
    def analyze_dataset_correlations(state: Dict[str, Any]):
        """Slow correlation analysis - MIC computation"""
        print(f"🔗 ANALYZE_DATASET_CORRELATIONS: Starting...")
        dataset_path = state["dataset_path"]
        
        # Load user drops if available
        user_drops = tools.load_user_drops(dataset_path)
        print(f"🔧 ANALYZE_DATASET_CORRELATIONS: User drops: {user_drops}")
        
        # Perform expensive correlation analysis
        print(f"⚡ ANALYZE_DATASET_CORRELATIONS: Computing correlations...")
        correlation_results = tools.analyze_dataset_correlations(dataset_path, user_drops)
        print(f"✅ ANALYZE_DATASET_CORRELATIONS: Computed {len(correlation_results.get('correlations', []))} correlations")
        
        # Store results in state
        state["correlations"] = correlation_results["correlations"]
        state["mic_pairs"] = correlation_results["mic_pairs"]
        
        print(f"🔗 ANALYZE_DATASET_CORRELATIONS: Complete - {len(state['correlations'])} correlations")
        return state
    
    def compute_warnings(state: Dict[str, Any]):
        """Compute data quality warnings"""
        print(f"⚠️ COMPUTE_WARNINGS: Starting...")
        dataset_card = state["dataset_card"]
        df_clean = state.get("df_clean")
        
        # Compute warnings with DataFrame for duplicate/imbalance analysis
        print(f"⚡ COMPUTE_WARNINGS: Computing warnings...")
        warnings = tools.simple_warnings(dataset_card, df_clean)
        print(f"✅ COMPUTE_WARNINGS: Found {len(warnings.items)} warnings")
        state["warnings"] = warnings
        print(f"⚠️ COMPUTE_WARNINGS: Complete - {len(warnings.items)} warnings")
        return state
    
    def llm_analysis_summary(state: Dict[str, Any]):
        """Generate LLM analysis summary from structured data."""
        print(f"📝 LLM_ANALYSIS_SUMMARY: Starting...")
        # Prepare context for LLM (no raw data)
        # Handle warnings - it might be a Warnings object or a dict due to serialization
        warnings_data = state["warnings"]
        
        if hasattr(warnings_data, 'items'):
            warnings_items = warnings_data.items
        elif isinstance(warnings_data, dict) and 'items' in warnings_data:
            warnings_items = warnings_data['items']
        else:
            warnings_items = warnings_data  # Fallback
        
        # Handle dataset_card - it might be serialized
        dataset_card = state["dataset_card"]
        if isinstance(dataset_card, dict):
            # It's been serialized, extract the needed values
            n_rows = dataset_card.get('n_rows', 0)
            n_cols = dataset_card.get('n_cols', 0)
            columns = dataset_card.get('columns', [])[:50]  # Limit to first 50 columns
        else:
            # It's a DatasetCard object
            n_rows = dataset_card.n_rows
            n_cols = dataset_card.n_cols
            columns = [c.model_dump() for c in dataset_card.columns[:50]]  # Limit to first 50 columns
        
        ctx = {
            "n_rows": n_rows,
            "n_cols": n_cols,
            "columns": columns,
            "warnings": warnings_items,
            "duplicates": state.get("duplicates", {"rate": 0.0}),
            "correlations_top": _get_top_correlations_from_dict(state.get("correlations", {}), k=5),
            "imbalances": state.get("imbalances", [])
        }
        
        try:
            # Try LLM generation
            print(f"🤖 LLM_ANALYSIS_SUMMARY: Attempting LLM generation...")
            raw = llm.json_only(SYSTEM_PROMPT_ANALYSIS, ctx, AnalysisSummary)
            state["analysis_summary"] = raw
            print(f"✅ LLM_ANALYSIS_SUMMARY: LLM generation successful")
        except Exception as e:
            # Fallback to rule-based summary
            print(f"⚠️ LLM_ANALYSIS_SUMMARY: LLM generation failed: {e}, using fallback")
            state["analysis_summary"] = llm.create_analysis_summary_fallback(
                dataset_card,
                warnings_items,
                _get_top_correlations_from_dict(state.get("correlations", {}), k=5),
                state.get("duplicates", {"rate": 0.0}).get("rate", 0.0)
            )
            print(f"✅ LLM_ANALYSIS_SUMMARY: Fallback generation complete")
        
        # Ensure the state has the right structure for run_page_graph
        state["warnings"] = state["warnings"]  # Already a Warnings object
        state["correlations"] = state.get("correlations", {})  # Already a dict
        state["analysis_summary"] = state["analysis_summary"]  # Already a dict
        
        print(f"📝 LLM_ANALYSIS_SUMMARY: Complete - Summary generated")
        
        return state
    
    # Create the graph
    g = StateGraph(dict)
    g.add_node("load_dataset_card", load_dataset_card)
    g.add_node("analyze_dataset_features", analyze_dataset_features)
    g.add_node("analyze_dataset_correlations", analyze_dataset_correlations)
    g.add_node("compute_warnings", compute_warnings)
    g.add_node("llm_analysis_summary", llm_analysis_summary)
    
    # Define the workflow - load dataset card first, then run analysis
    g.set_entry_point("load_dataset_card")
    g.add_edge("load_dataset_card", "analyze_dataset_features")
    g.add_edge("analyze_dataset_features", "analyze_dataset_correlations")
    g.add_edge("analyze_dataset_correlations", "compute_warnings")  # Run sequentially to avoid conflicts
    g.add_edge("compute_warnings", "llm_analysis_summary")
    g.add_edge("llm_analysis_summary", END)
    
    return g.compile()



def _get_top_correlations_from_dict(correlations_data, k: int = 5) -> List[Dict[str, Any]]:
    """
    Get top K correlations from either a dictionary format or list format.
    
    Args:
        correlations_data: Dictionary with correlation matrix OR list of correlation pairs
        k: Number of top correlations to return
        
    Returns:
        List of correlation pairs with values
    """
    if isinstance(correlations_data, list):
        # Already a list of correlation pairs, just return top k
        return correlations_data[:k]
    elif isinstance(correlations_data, dict):
        # Dictionary format - convert to list
        flat = []
        for a, row in correlations_data.items():
            for b, v in row.items():
                if a < b:  # Avoid duplicates and self-correlations
                    flat.append((a, b, abs(v), v))
        
        # Sort by absolute correlation value
        flat.sort(key=lambda x: x[2], reverse=True)
        
        return [
            {"pair": [a, b], "value": v}
            for a, b, _, v in flat[:k]
        ]
    else:
        # Fallback - return empty list
        return []


# System prompt for LLM analysis summary
SYSTEM_PROMPT_ANALYSIS = """
You are a data analysis copilot. You will receive ANALYSIS_CONTEXT (JSON) with:
- n_rows, n_cols: Dataset dimensions
- columns: List of column metadata (name, dtype, missing_pct, unique)
- warnings: List of data quality issues
- duplicates: Object with row_rate
- correlations_top: Top correlation pairs
- imbalances: Class imbalance information

Your task:
1) Write a concise 3-6 sentence paragraph summarizing key data issues and relationships
2) Mention only facts present in the input - no speculation
3) If duplicates.row_rate > 0, quantify and suggest de-duplication
4) Mention the strongest correlation if provided
5) Call out any column with missing_pct > 20%
6) If imbalances exist, mention the most skewed one

Return JSON ONLY matching this exact schema:
{
  "paragraph": "string",
  "bullets": ["string", "string"],
  "callouts": [{"type": "string", "value": number_or_string, "note": "string"}]
}

Be concise, neutral, and avoid jargon. Use temperature=0 for consistency.
"""


