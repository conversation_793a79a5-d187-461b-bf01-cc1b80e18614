"""
Pydantic schemas for data contracts in the orchestration system.

These schemas ensure type safety and validation for all data structures
used in the analysis workflows.
"""

from pydantic import BaseModel
from typing import List, Literal, Optional, Dict, Any


DType = Literal["numeric", "categorical", "datetime", "text", "unknown"]


class ColumnCard(BaseModel):
    """Schema for individual column metadata"""
    name: str
    dtype: DType
    unique: int
    missing_pct: float
    notes: Optional[str] = None


class DatasetCard(BaseModel):
    """Schema for dataset overview and metadata"""
    dataset_id: str
    n_rows: int
    n_cols: int
    columns: List[ColumnCard]
    time_columns: List[str] = []
    pii_flags: List[str] = []
    warnings: List[Dict[str, Any]] = []
    user_context: str = "" # User-provided business context
    # Track dropped columns for stale propagation
    dropped_columns: List[str] = []
    original_columns: List[str] = []


class Warnings(BaseModel):
    """Schema for data quality warnings"""
    items: List[Dict[str, Any]] = []  # {column, issue, severity, hint}


class Correlations(BaseModel):
    """Schema for correlation matrix results"""
    method: Literal["MIC", "pearson"] = "MIC" # Method used to compute correlations
    matrix: Dict[str, Dict[str, float]]  # {col: {col: corr}}


class AnalysisSummary(BaseModel):
    """Schema for LLM-generated analysis summary"""
    paragraph: str
    bullets: List[str] = []
    callouts: List[Dict[str, Any]] = []  # e.g., {"type":"duplicate_rows","value":0.12,"note":"12% duplicates"}


class PageView(BaseModel):
    """Schema for page view response with artifacts"""
    artifacts: Dict[str, Dict[str, Any]] = {}  # kind -> {artifact_id, payload}
    stale: bool = False
    message: Optional[str] = None
