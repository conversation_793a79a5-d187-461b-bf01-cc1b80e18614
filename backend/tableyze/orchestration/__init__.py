"""
Orchestration package for page-centric data analysis workflows.

This package provides:
- Pydantic schemas for data contracts
- Pure data analysis tools
- LLM integration with JSON-only responses
- LangGraph-based workflow orchestration
- Artifact management and session handling
"""

from .schemas import (
    ColumnCard,
    DatasetCard,
    Warnings,
    Correlations,
    AnalysisSummary,
    PageView,
)
from .tools import (
    build_dataset_card,
    simple_warnings,
    duplicate_row_rate,
    top_abs_correlations,
    quick_imbalances,
)
from .llm import json_only
from .graphs import upload_graph, analysis_graph
# Import runners only when needed to avoid Django dependency
# from .runners import run_page_graph, view_page, resolve_dataset_path

__all__ = [
    # Schemas
    "ColumnCard",
    "DatasetCard", 
    "Warnings",
    "Correlations",
    "AnalysisSummary",
    "PageView",
    # Tools
    "build_dataset_card",
    "compute_correlations",
    "simple_warnings",
    "duplicate_row_rate",
    "top_abs_correlations",
    "quick_imbalances",
    # LLM
    "json_only",
    # Graphs
    "upload_graph",
    "analysis_graph",
    # Runners (imported dynamically to avoid Django dependency)
    # "run_page_graph",
    # "view_page", 
    # "resolve_dataset_path",
]
