"""
Pure data analysis tools for the orchestration system.

These functions perform data analysis operations without side effects,
returning structured results that can be stored as artifacts.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple
from .schemas import DatasetCard, ColumnCard, Correlations, Warnings
import json
import os
from minepy import MINE
from sklearn.preprocessing import LabelEncoder


# PII detection patterns
PII_NAMES = {
    "email", "phone", "ssn", "ticket", "uuid", "name", "passport", 
    "natid", "iban", "address", "id", "user_id", "customer_id"
}


def build_dataset_card(dataset_path: str, dataset_id: str, user_context: str = "") -> DatasetCard:
    """
    Build a comprehensive dataset card with column metadata.
    
    Args:
        dataset_path: Path to the dataset file
        dataset_id: Unique identifier for the dataset
        user_context: User-provided business context
        
    Returns:
        DatasetCard with comprehensive metadata including dropped columns
    """
    # Read dataset
    if dataset_path.lower().endswith(".csv"):
        df = pd.read_csv(dataset_path)
    else:
        df = pd.read_excel(dataset_path)
    
    # Get user-selected columns to drop
    user_drops = load_user_drops(dataset_path)
    
    # Auto-detect ID-like columns to drop
    auto_drops = []
    for col in df.columns:
        if col in user_drops:
            continue
        non_null_count = df[col].count()
        unique_count = df[col].nunique()
        if non_null_count > 0 and unique_count == non_null_count:
            auto_drops.append(col)
    
    # Combine user and auto drops
    all_drops = list(set(user_drops + auto_drops))
    
    # Drop columns and build column cards for remaining columns
    df_clean = df.drop(columns=all_drops, errors='ignore')
    cols = []
    
    for c in df_clean.columns:
        s = df_clean[c]
        
        # Determine data type
        if pd.api.types.is_numeric_dtype(s):
            dtype = "numeric"
        elif pd.api.types.is_datetime64_any_dtype(s):
            dtype = "datetime"
        elif s.dtype == object and (s.astype(str).str.len().mean() > 30):
            dtype = "text"
        else:
            dtype = "categorical"
        
        # Calculate statistics
        unique_count = int(s.nunique(dropna=True))
        missing_pct = float(s.isna().mean() * 100)
        
        cols.append(ColumnCard(
            name=c,
            dtype=dtype,
            unique=unique_count,
            missing_pct=missing_pct
        ))
    
    # Identify time columns and PII from remaining columns
    time_cols = [cc.name for cc in cols if cc.dtype == "datetime"]
    pii = [cc.name for cc in cols if any(pii_name in cc.name.lower() for pii_name in PII_NAMES)]
    
    return DatasetCard(
        dataset_id=str(dataset_id),
        n_rows=int(df.shape[0]),
        n_cols=int(df_clean.shape[1]),  # Use cleaned column count
        columns=cols,
        time_columns=time_cols,
        pii_flags=pii,
        user_context=user_context,
        dropped_columns=all_drops,
        original_columns=list(df.columns)
    )



def simple_warnings(card: DatasetCard, df: pd.DataFrame = None) -> Warnings:
    """
    Generate data quality warnings based on dataset card.
    
    Args:
        card: DatasetCard to analyze
        df: Optional DataFrame for additional analysis (duplicates, imbalances)
        
    Returns:
        Warnings object with issues found
    """
    items = []
    
    for c in card.columns:
        # High missingness
        if c.missing_pct > 50:
            items.append({
                "column": c.name,
                "issue": "high_missingness",
                "severity": "high",
                "hint": "Consider imputing or dropping this column"
            })
        
        # PII detection
        if c.name in card.pii_flags:
            items.append({
                "column": c.name,
                "issue": "pii_like",
                "severity": "medium",
                "hint": "Consider masking or excluding from modeling"
            })
        
        # ID-like columns (all unique values)
        if c.unique == card.n_rows:
            items.append({
                "column": c.name,
                "issue": "id_like",
                "severity": "medium",
                "hint": "Likely an ID column - exclude from modeling"
            })
        
        # Very low cardinality categorical
        if c.dtype == "categorical" and c.unique == 1:
            items.append({
                "column": c.name,
                "issue": "constant_column",
                "severity": "low",
                "hint": "Column has only one unique value"
            })
        
        # Binary imbalance for 2-class candidates (flag extreme <1% positives)
        if (df is not None and c.dtype == "categorical" and c.unique == 2 and 
            c.name in df.columns):
            value_counts = df[c.name].value_counts(normalize=True, dropna=True)
            if len(value_counts) == 2:
                min_ratio = value_counts.min()
                if min_ratio < 0.01:  # Less than 1% in minority class
                    items.append({
                        "column": c.name,
                        "issue": "binary_imbalance",
                        "severity": "high",
                        "hint": f"Extreme class imbalance: {min_ratio:.1%} in minority class"
                    })
    
    # Add duplicate rows warning if DataFrame provided
    if df is not None:
        duplicate_rate = df.duplicated().mean()
        if duplicate_rate > 0.05:  # More than 5% duplicates
            items.append({
                "issue": "duplicate_rows",
                "rate": float(duplicate_rate),
                "severity": "medium" if duplicate_rate < 0.2 else "high",
                "hint": f"Drop duplicates ({duplicate_rate:.1%} of rows)"
            })
    
    return Warnings(items=items)


def duplicate_row_rate(df: pd.DataFrame) -> float:
    """
    Calculate the rate of duplicate rows in the dataset.
    
    Args:
        df: DataFrame to analyze
        
    Returns:
        Float between 0 and 1 representing duplicate rate
    """
    return float(df.duplicated().mean())


def top_abs_correlations(corr: Correlations, k: int = 5) -> List[Dict[str, Any]]:
    """
    Get top K correlations by absolute value.
    
    Args:
        corr: Correlations object
        k: Number of top correlations to return
        
    Returns:
        List of correlation pairs with values
    """
    flat = []
    for a, row in corr.matrix.items():
        for b, v in row.items():
            if a < b:  # Avoid duplicates and self-correlations
                flat.append((a, b, abs(v), v))
    
    # Sort by absolute correlation value
    flat.sort(key=lambda x: x[2], reverse=True)
    
    return [
        {"pair": [a, b], "value": v}
        for a, b, _, v in flat[:k]
    ]


def quick_imbalances(df: pd.DataFrame, card: DatasetCard) -> List[Dict[str, Any]]:
    """
    Detect class imbalances in categorical columns.
    
    Args:
        df: DataFrame to analyze
        card: DatasetCard with column metadata
        
    Returns:
        List of imbalance information for categorical columns
    """
    out = []
    
    # Only check categorical columns with reasonable cardinality
    categorical_cols = [
        c.name for c in card.columns 
        if c.dtype == "categorical" and 2 <= c.unique <= 12
    ]
    
    for col in categorical_cols:
        if col in df.columns:
            value_counts = df[col].value_counts(normalize=True, dropna=True)
            if not value_counts.empty:
                out.append({
                    "column": col,
                    "values": {str(k): float(v) for k, v in value_counts.items()}
                })
    
    return out


def compute_mic(df: pd.DataFrame) -> dict[tuple[str, str], float]:
    """
    Compute the Maximal Information Coefficient (MIC) for all pairs of columns 
    in a mixed-type DataFrame (numeric + categorical).
    
    This matches the approach in helpers.py exactly.
    
    Parameters
    ----------
    df : pd.DataFrame
        Input dataset with mixed types.
    
    Returns
    -------
    dict[tuple[str, str], float]
        Dictionary of MIC values where both (col1, col2) and (col2, col1) exist.
    """
    
    # Encode categorical variables as integers (same as helpers.py)
    encoded_df = df.copy()
    for col in encoded_df.columns:
        if encoded_df[col].dtype == "object" or str(encoded_df[col].dtype).startswith("category"):
            encoded_df[col] = LabelEncoder().fit_transform(encoded_df[col].astype(str))
    
    cols = encoded_df.columns
    n = len(cols)
    mic_dict: dict[tuple[str, str], float] = {}
    
    mine = MINE(alpha=0.6, c=15)  # Default parameters from Reshef et al.
    
    for i in range(n):
        for j in range(i, n):
            x = encoded_df.iloc[:, i].values
            y = encoded_df.iloc[:, j].values
            
            mine.compute_score(x, y)
            mic = mine.mic()
            
            col1, col2 = cols[i], cols[j]
            mic_dict[(col1, col2)] = mic
            mic_dict[(col2, col1)] = mic  # Explicit symmetric entry
    
    return mic_dict


def get_top_mic_pairs(mic_pairs: dict, top_n: int = 5) -> list[dict]:
    """
    Compute the MIC dictionary for all features (numeric + categorical) 
    and return the top correlated feature pairs.
    
    This matches the approach in helpers.py exactly.

    Parameters
    ----------
    mic_pairs : dict
        Dictionary of MIC values from compute_mic()
    top_n : int, optional
        Number of top pairs to return (default = 5).

    Returns
    -------
    list[dict]
        List of top correlated feature pairs with MIC scores.
    """
    corr_pairs = []
    seen = set()
    for (col1, col2), mic_value in mic_pairs.items():
        if col1 == col2 or (col2, col1) in seen:
            continue
        seen.add((col1, col2))
        corr_pairs.append({
            "feature1": col1,
            "feature2": col2,
            "correlation": float(mic_value),
            "correlation_raw": float(mic_value)
        })
    
    # Sort by MIC and take top N
    corr_pairs.sort(key=lambda x: x["correlation"], reverse=True)
    return corr_pairs[:top_n]


def analyze_dataset_features(dataset_path: str, user_drops: List[str] = None) -> Dict[str, Any]:
    """
    Comprehensive dataset analysis including features, statistics, and histograms.
    This replaces the legacy dataset_analyze function.
    
    Args:
        dataset_path: Path to the dataset file
        user_drops: List of columns to drop (user-selected)
        
    Returns:
        Dictionary with comprehensive analysis results
    """
    # Read dataset
    if dataset_path.lower().endswith(".csv"):
        df = pd.read_csv(dataset_path)
    else:
        df = pd.read_excel(dataset_path)
    
    # Get user-selected columns to drop
    user_drops = user_drops or []
    
    # Auto-detect categorical columns with unique values = row count (excluding NaN)
    auto_drops = []
    for col in df.columns:
        if col in user_drops:
            continue
        non_null_count = df[col].count()  # excludes NaN
        unique_count = df[col].nunique()  # excludes NaN
        if non_null_count > 0 and unique_count == non_null_count:
            # Each non-null value is unique - likely an ID column
            auto_drops.append(col)
    
    # Drop columns
    all_drops = list(set(user_drops + auto_drops))
    df_clean = df.drop(columns=all_drops, errors='ignore')
    
    # Analyze remaining columns
    features = []
    numerical_cols = []
    categorical_cols = []
    
    for col in df_clean.columns:
        feature_info = {'name': col, 'type': 'unknown', 'stats': {}, 'histogram': {}}
        
        # Determine column type
        if pd.api.types.is_numeric_dtype(df_clean[col]):
            feature_info['type'] = 'numerical'
            numerical_cols.append(col)
            
            # Calculate numerical statistics
            col_data = df_clean[col].dropna()
            if len(col_data) > 0:
                feature_info['stats'] = {
                    'mean': float(col_data.mean()),
                    'median': float(col_data.median()),
                    'min': float(col_data.min()),
                    'max': float(col_data.max()),
                    'std': float(col_data.std()) if len(col_data) > 1 else 0.0,
                    'count': int(len(col_data)),
                    'null_count': int(df_clean[col].isnull().sum())
                }
                
                # Create histogram
                hist, bin_edges = np.histogram(col_data, bins=min(20, len(col_data.unique())))
                feature_info['histogram'] = {
                    'counts': hist.tolist(),
                    'bin_edges': bin_edges.tolist()
                }
                
        elif pd.api.types.is_datetime64_any_dtype(df_clean[col]):
            feature_info['type'] = 'date'
            col_data = df_clean[col].dropna()
            feature_info['stats'] = {
                'count': int(len(col_data)),
                'null_count': int(df_clean[col].isnull().sum()),
                'min_date': str(col_data.min()) if len(col_data) > 0 else None,
                'max_date': str(col_data.max()) if len(col_data) > 0 else None
            }
            
        else:
            # Categorical or text
            feature_info['type'] = 'categorical'
            categorical_cols.append(col)
            
            col_data = df_clean[col].dropna()
            if len(col_data) > 0:
                value_counts = col_data.value_counts()
                feature_info['stats'] = {
                    'unique_count': int(col_data.nunique()),
                    'most_common': str(value_counts.index[0]) if len(value_counts) > 0 else None,
                    'most_common_count': int(value_counts.iloc[0]) if len(value_counts) > 0 else 0,
                    'count': int(len(col_data)),
                    'null_count': int(df_clean[col].isnull().sum()),
                }
                
                # Create histogram for categorical (top 10 categories)
                top_categories = value_counts.head(10)
                feature_info['histogram'] = {
                    'categories': top_categories.index.tolist(),
                    'counts': top_categories.values.tolist()
                }
        
        features.append(feature_info)
    
    # Additions: Duplicates, class imbalances for Data warnings
    
    # Calculate duplicate rows
    duplicate_count = df_clean.duplicated().sum()
    duplicate_rate = duplicate_count / len(df_clean) if len(df_clean) > 0 else 0
    
    # Calculate class imbalances for binary categorical columns
    imbalances = []
    for col in categorical_cols:
        if df_clean[col].nunique() == 2:
            value_counts = df_clean[col].value_counts()
            if len(value_counts) == 2:
                min_count = value_counts.min()
                max_count = value_counts.max()
                imbalance_ratio = min_count / max_count if max_count > 0 else 0
                if imbalance_ratio < 0.1:  # Less than 10% in minority class
                    imbalances.append({
                        'column': col,
                        'ratio': float(imbalance_ratio),
                        'minority_class': value_counts.idxmin(),
                        'minority_count': int(min_count),
                        'majority_count': int(max_count)
                    })
    
    return {
        'features': features,
        'duplicates': {
            'count': int(duplicate_count),
            'rate': float(duplicate_rate)
        },
        'imbalances': imbalances,
        'dropped_columns': {
            'user_selected': user_drops,
            'auto_detected': auto_drops,
            'total': all_drops
        },
        'summary': {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'clean_rows': len(df_clean),
            'clean_columns': len(df_clean.columns),
            'numerical_columns': len(numerical_cols),
            'categorical_columns': len(categorical_cols)
        },
        'df_clean': df_clean,  # Pass the cleaned DataFrame for warnings analysis
        'numerical_cols': numerical_cols,
        'categorical_cols': categorical_cols
    }


def analyze_dataset_correlations(dataset_path: str, user_drops: List[str] = None) -> Dict[str, Any]:
    """
    Compute correlations for dataset features (separate from feature statistics).
    
    This function focuses only on the expensive correlation computations,
    allowing feature statistics to be delivered quickly while correlations
    are computed separately.
    
    Args:
        dataset_path: Path to the dataset file
        user_drops: List of columns to drop (user-selected)
        
    Returns:
        Dictionary with correlation analysis results
    """
    # Read dataset
    if dataset_path.lower().endswith(".csv"):
        df = pd.read_csv(dataset_path)
    else:
        df = pd.read_excel(dataset_path)
    
    # Get user-selected columns to drop
    user_drops = user_drops or []
    
    # Auto-detect categorical columns with unique values = row count (excluding NaN)
    auto_drops = []
    for col in df.columns:
        if col in user_drops:
            continue
        non_null_count = df[col].count()  # excludes NaN
        unique_count = df[col].nunique()  # excludes NaN
        if non_null_count > 0 and unique_count == non_null_count:
            # Each non-null value is unique - likely an ID column
            auto_drops.append(col)
    
    # Drop columns
    all_drops = list(set(user_drops + auto_drops))
    df_clean = df.drop(columns=all_drops, errors='ignore')
    
    # Identify column types for correlation analysis
    numerical_cols = []
    categorical_cols = []
    
    for col in df_clean.columns:
        if pd.api.types.is_numeric_dtype(df_clean[col]):
            numerical_cols.append(col)
        elif not pd.api.types.is_datetime64_any_dtype(df_clean[col]):
            categorical_cols.append(col)
    
    # Calculate correlations between all non-date columns
    correlations = []
    non_date_cols = numerical_cols + categorical_cols
    df_for_mic = df_clean[non_date_cols].iloc[:10000]  # Limit for performance
    mic_pairs = compute_mic(df_for_mic)
    correlations = get_top_mic_pairs(mic_pairs, top_n=5)
    
    return {
        'correlations': correlations,  # List format for frontend compatibility
        'mic_pairs': mic_pairs
    }


def load_user_drops(dataset_path: str) -> List[str]:
    """
    Load user-selected columns to drop from sidecar JSON file.
    
    Args:
        dataset_path: Path to the dataset file
        
    Returns:
        List of column names to drop
    """
    sidecar = dataset_path + '.drops.json'
    if os.path.exists(sidecar):
        try:
            with open(sidecar, 'r') as f:
                data = json.load(f)
                return data.get('drop_columns', [])
        except Exception as e:
            print(f"Error loading user drops: {e}")
    return []


def save_user_drops(dataset_path: str, drop_columns: List[str]) -> None:
    """
    Save user-selected columns to drop to sidecar JSON file.
    
    Args:
        dataset_path: Path to the dataset file
        drop_columns: List of column names to drop
    """
    sidecar = dataset_path + '.drops.json'
    try:
        with open(sidecar, 'w') as f:
            json.dump({'drop_columns': drop_columns}, f)
    except Exception as e:
        print(f"Error saving user drops: {e}")
