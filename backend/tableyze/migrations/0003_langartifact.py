# Generated by Django 4.2.7 on 2025-10-14 12:30

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("tableyze", "0002_alter_analysis_options_remove_customer_address_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="LangArtifact",
            fields=[
                (
                    "id",
                    models.Char<PERSON>ield(
                        help_text="SHA256 of payload or UUID",
                        max_length=64,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "page",
                    models.Char<PERSON>ield(
                        help_text="Page identifier: upload, analysis, etc.",
                        max_length=50,
                    ),
                ),
                (
                    "kind",
                    models.CharField(
                        help_text="Artifact type: dataset_card, warnings, etc.",
                        max_length=50,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ready", "Ready"),
                            ("stale", "Stale"),
                            ("running", "Running"),
                            ("error", "Error"),
                        ],
                        default="running",
                        max_length=20,
                    ),
                ),
                (
                    "depends_on",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        default=list, help_text="List of artifact ids this depends on"
                    ),
                ),
                (
                    "payload",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        blank=True, help_text="Small JSON results", null=True
                    ),
                ),
                (
                    "uri",
                    models.<PERSON>r<PERSON>ield(
                        blank=True,
                        help_text="For large objects",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "metrics",
                    models.JSONField(
                        blank=True, help_text="Timings, sizes, etc.", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="artifacts",
                        to="tableyze.analysis",
                    ),
                ),
            ],
            options={
                "verbose_name": "LangArtifact",
                "verbose_name_plural": "LangArtifacts",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["analysis", "page", "kind", "status"],
                        name="tableyze_la_analysi_2a583e_idx",
                    )
                ],
            },
        ),
    ]
