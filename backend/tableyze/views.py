from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>art<PERSON><PERSON><PERSON>, FormParser
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User
from django.conf import settings
import os
import uuid
import json

from .models import Analysis, Dataset, Customer, LangArtifact
from .serializers import (
    AnalysisSerializer,
    AnalysisDetailSerializer,
    DatasetSerializer,
    CustomerSerializer
)
from .helpers import compute_mic, get_top_mic_pairs
from .orchestration.runners import *
import pandas as pd
import numpy as np
import json


class AnalysisListCreateView(generics.ListCreateAPIView):
    """
    List all analyses for the authenticated user or create a new analysis
    """
    serializer_class = AnalysisSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development

    def get_queryset(self):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        return Analysis.objects.filter(user=user, is_active=True)

    def perform_create(self, serializer):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        serializer.save(user=user)


class AnalysisDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete an analysis instance
    """
    serializer_class = AnalysisDetailSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development

    def get_queryset(self):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        return Analysis.objects.filter(user=user)

    def perform_destroy(self, instance):
        """Delete analysis and all related data (datasets, files, artifacts)"""
        # Get all datasets for this analysis
        datasets = instance.datasets.all()
        
        for dataset in datasets:
            # Delete the physical file
            try:
                file_url = dataset.file_url or ''
                media_url = getattr(settings, 'MEDIA_URL', '/media/')
                media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
                
                if file_url.startswith(media_url):
                    rel_path = file_url[len(media_url):]
                    file_path = os.path.join(media_root, rel_path)
                else:
                    file_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)
                
                # Delete the file if it exists
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"Deleted file: {file_path}")
                
                # Delete sidecar files
                sidecar_extensions = ['.drops.json', '.user_context.json', '.correlation.json', '.target.json']
                for ext in sidecar_extensions:
                    sidecar_path = file_path + ext
                    if os.path.exists(sidecar_path):
                        os.remove(sidecar_path)
                        
            except Exception as e:
                print(f"Error deleting file: {e}")
        
        # Delete all related artifacts (LangArtifact model)
        instance.artifacts.all().delete()
        
        # Delete all datasets (cascade will happen automatically via foreign key)
        instance.datasets.all().delete()
        
        # Now delete the analysis itself
        instance.delete()


class DatasetListCreateView(generics.ListCreateAPIView):
    """
    List all datasets for an analysis or create a new dataset. On create, accept
    multipart file under key 'file', store locally, and return a basic preview
    of the first 5 rows using pandas (if installed).
    """
    serializer_class = DatasetSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        analysis_id = self.kwargs.get('analysis_id')
        return Dataset.objects.filter(
            analysis_id=analysis_id,
            user=user
        )

    def post(self, request, *args, **kwargs):
        # emulate create with custom handling
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        analysis_id = self.kwargs.get('analysis_id')
        analysis = get_object_or_404(Analysis, id=analysis_id, user=user)

        uploaded_file = request.FILES.get('file')
        name = request.data.get('name') or (uploaded_file.name if uploaded_file else None)
        if not uploaded_file or not name:
            return Response({'detail': 'file and name are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Validate size and extension
        max_bytes = 100 * 1024 * 1024
        if uploaded_file.size > max_bytes:
            return Response({'detail': 'File too large (>100MB).'}, status=status.HTTP_400_BAD_REQUEST)
        _, ext = os.path.splitext(uploaded_file.name.lower())
        if ext not in ['.csv', '.xls', '.xlsx']:
            return Response({'detail': 'Unsupported file type.'}, status=status.HTTP_400_BAD_REQUEST)

        # Save locally under MEDIA_ROOT
        media_root = getattr(settings, 'MEDIA_ROOT', None)
        media_url = getattr(settings, 'MEDIA_URL', '/media/')
        if not media_root:
            # fallback to project base images dir (as configured in settings for local)
            media_root = os.path.join(settings.BASE_DIR, 'images')
            os.makedirs(media_root, exist_ok=True)
        os.makedirs(media_root, exist_ok=True)

        uid = uuid.uuid4().hex
        stored_name = f"datasets/{uid}-{uploaded_file.name}"
        stored_path = os.path.join(media_root, stored_name)
        os.makedirs(os.path.dirname(stored_path), exist_ok=True)
        with open(stored_path, 'wb') as dest:
            for chunk in uploaded_file.chunks():
                dest.write(chunk)

        file_url = f"{media_url}{stored_name}" if media_url else stored_path

        # Create dataset
        dataset = Dataset.objects.create(
            user=user,
            analysis=analysis,
            name=name,
            file_url=file_url,
            status='raw'
        )

        dataset_card_id = None
        try:
            dataset_path = resolve_dataset_path(file_url)
            state = {
                'dataset_path': dataset_path,
                'dataset_id': str(dataset.id),
                'user_context': request.data.get('user_context', ''),
            }
            artifact = run_page_graph(analysis, 'upload', state)
            if artifact:
                dataset_card_id = artifact.id
        except Exception as e:
            print(f"Failed to run upload workflow: {e}")
            dataset_card_id = None

        # Basic parsing for preview
        preview = {'columns': [], 'rows': []}
        try:
            abs_path = stored_path
            # pandas requires absolute path
            if not os.path.isabs(abs_path):
                abs_path = os.path.abspath(abs_path)
            if ext == '.csv':
                df = pd.read_csv(abs_path, nrows=5)
            else:
                df = pd.read_excel(abs_path, nrows=5)
            preview['columns'] = list(map(str, df.columns.tolist()))
            preview['rows'] = [list(map(lambda v: '' if pd.isna(v) else str(v), row)) for row in df.values.tolist()]
        except Exception as e:
            print(f"Failed to parse dataset: {e}")
            preview = {'columns': [], 'rows': []}

        serialized = DatasetSerializer(dataset).data
        return Response({'dataset': serialized, 'preview': preview, 'dataset_card_artifact_id': dataset_card_id}, status=status.HTTP_201_CREATED)

    def perform_create(self, serializer):
        # Not used due to custom post
        pass


class DatasetDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a dataset instance
    """
    serializer_class = DatasetSerializer
    permission_classes = [AllowAny]  # Temporarily allow any access for development

    def get_queryset(self):
        # Get or create a default user for development
        user, created = User.objects.get_or_create(
            username='demo_user',
            defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
        )
        return Dataset.objects.filter(user=user)

    def perform_destroy(self, instance):
        """Delete dataset and all related files and artifacts"""
        # Delete the physical file
        try:
            file_url = instance.file_url or ''
            media_url = getattr(settings, 'MEDIA_URL', '/media/')
            media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
            
            if file_url.startswith(media_url):
                rel_path = file_url[len(media_url):]
                file_path = os.path.join(media_root, rel_path)
            else:
                file_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)
            
            # Delete the file if it exists
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"Deleted file: {file_path}")
            
            # Delete sidecar files
            sidecar_extensions = ['.drops.json', '.user_context.json']
            for ext in sidecar_extensions:
                sidecar_path = file_path + ext
                if os.path.exists(sidecar_path):
                    os.remove(sidecar_path)
                    
        except Exception as e:
            print(f"Error deleting file: {e}")
        
        # Delete related artifacts for this specific dataset
        analysis = instance.analysis
        # Filter artifacts related to this dataset
        LangArtifact.objects.filter(
            analysis=analysis,
            kind__in=['dataset_card', 'features', 'correlations', 'warnings', 'analysis_summary']
        ).delete()
        
        # Delete the dataset record
        instance.delete()


@api_view(['GET'])
@permission_classes([AllowAny])
def dataset_preview(request, pk):
    """
    Return the first 5 rows preview for the given dataset.
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)

    # Derive absolute path from file_url
    file_url = dataset.file_url or ''
    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')

    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        abs_path = os.path.join(media_root, rel_path)
    else:
        # fallback: maybe absolute path already
        abs_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    # Parse using pandas (first 5 rows only)
    preview = {'columns': [], 'rows': []}
    try:
        _, ext = os.path.splitext(abs_path.lower())
        if ext == '.csv':
            df = pd.read_csv(abs_path, nrows=5)
        else:
            df = pd.read_excel(abs_path, nrows=5)
        preview['columns'] = list(map(str, df.columns.tolist()))
        preview['rows'] = [list('' if pd.isna(v) else str(v) for v in row) for row in df.values.tolist()]
    except Exception as e:
        preview = {'columns': [], 'rows': []}

    return Response({'preview': preview})


@api_view(['GET', 'PATCH'])
@permission_classes([AllowAny])
def dataset_drop_columns(request, pk):
    """
    Persist and retrieve selected columns to drop for a dataset.
    For now, store in a sidecar JSON file next to the dataset path to avoid DB migration.
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)

    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
    file_url = dataset.file_url or ''
    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        dataset_path = os.path.join(media_root, rel_path)
    else:
        dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    sidecar = dataset_path + '.drops.json'

    if request.method == 'PATCH':
        try:
            body = request.data.get('drop_columns')
            if isinstance(body, str):
                cols = json.loads(body)
            else:
                cols = body or []
            os.makedirs(os.path.dirname(sidecar), exist_ok=True)
            with open(sidecar, 'w') as f:
                json.dump({'drop_columns': cols}, f)
            return Response({'drop_columns': cols})
        except Exception:
            return Response({'detail': 'Failed to persist drop columns.'}, status=status.HTTP_400_BAD_REQUEST)

    # GET
    try:
        if os.path.exists(sidecar):
            with open(sidecar, 'r') as f:
                data = json.load(f)
            return Response({'drop_columns': data.get('drop_columns', [])})
    except Exception:
        pass
    return Response({'drop_columns': []})


@api_view(['GET', 'PATCH'])
@permission_classes([AllowAny])
def dataset_target_selection_persist(request, pk):
    """
    Persist and retrieve selected target variable for a dataset.
    Store in a sidecar JSON file next to the dataset path to avoid DB migration.
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)

    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
    file_url = dataset.file_url or ''
    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        dataset_path = os.path.join(media_root, rel_path)
    else:
        dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    sidecar = dataset_path + '.target.json'

    if request.method == 'PATCH':
        try:
            body = request.data.get('target_selection')
            # target_selection should be None or a dict with target info
            os.makedirs(os.path.dirname(sidecar), exist_ok=True)
            with open(sidecar, 'w') as f:
                json.dump({'target_selection': body}, f)
            return Response({'target_selection': body})
        except Exception:
            return Response({'detail': 'Failed to persist target selection.'}, status=status.HTTP_400_BAD_REQUEST)

    # GET
    try:
        if os.path.exists(sidecar):
            with open(sidecar, 'r') as f:
                data = json.load(f)
            return Response({'target_selection': data.get('target_selection', None)})
    except Exception:
        pass
    return Response({'target_selection': None})


@api_view(['GET', 'PATCH'])
@permission_classes([AllowAny])
def dataset_user_context(request, pk):
    """
    Persist and retrieve textarea content for a dataset.
    Stores in a sidecar JSON file next to the dataset file (no DB migration needed).
    Also updates the DatasetCard artifact with the new user context when changed.
    """
    # Get or create a default user for development
    user, _ = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)

    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
    file_url = dataset.file_url or ''
    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        dataset_path = os.path.join(media_root, rel_path)
    else:
        dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    sidecar = dataset_path + '.user_context.json'

    if request.method == 'PATCH':
        try:
            text_content = request.data.get('user_context', '')
            os.makedirs(os.path.dirname(sidecar), exist_ok=True)
            with open(sidecar, 'w') as f:
                json.dump({'user_context': text_content}, f)
            
            # Update the DatasetCard artifact with new user context
            from .models import LangArtifact
            
            # Find the current dataset_card artifact for this analysis
            current_dataset_card = LangArtifact.objects.filter(
                analysis=dataset.analysis,
                page='upload',
                kind='dataset_card',
                status='ready'
            ).order_by('-created_at').first()
            
            if current_dataset_card:
                # Update the payload with new user_context in place
                # We keep the same artifact ID since user_context is metadata, not core data
                updated_payload = dict(current_dataset_card.payload)
                updated_payload['user_context'] = text_content
                
                # Update existing artifact in place
                current_dataset_card.payload = updated_payload
                current_dataset_card.save()
            
            return Response({'user_context': text_content})
        except Exception as e:
            return Response({'detail': f'Failed to persist textarea content: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

    # GET
    try:
        if os.path.exists(sidecar):
            with open(sidecar, 'r') as f:
                data = json.load(f)
            return Response({'user_context': data.get('user_context', '')})
    except Exception:
        pass
    return Response({'user_context': ''})


@api_view(['POST'])
@permission_classes([AllowAny])
def dataset_analyze(request, pk):
    """
    Start comprehensive data analysis using LangGraph workflow.
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)
    analysis = dataset.analysis

    # Get file path
    media_url = getattr(settings, 'MEDIA_URL', '/media/')
    media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
    file_url = dataset.file_url or ''
    if file_url.startswith(media_url):
        rel_path = file_url[len(media_url):]
        dataset_path = os.path.join(media_root, rel_path)
    else:
        dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

    try:
        # Prepare state for analysis workflow
        state = {
            'dataset_path': dataset_path,
            'dataset_id': str(dataset.id),
            'user_context': request.data.get('user_context', ''),
        }
        
        # Start the streaming analysis workflow
        artifacts = run_page_graph(analysis, 'analysis', state)
        
        return Response({
            'message': 'Analysis started successfully',
            'artifacts_created': len(artifacts) if artifacts else 0,
            'artifact_ids': [a.id for a in artifacts] if artifacts else [],
            'session_id': str(analysis.id)
        })

    except Exception as e:
        return Response({
            'detail': f'Analysis failed: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([AllowAny])
def analysis_status(request, pk):
    """
    Get the current status and results of analysis for a dataset.
    Automatically starts analysis if dataset is uploaded but analysis hasn't begun.
    Returns processed, frontend-ready data.
    """
    from .orchestration.runners import run_page_graph
    
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )
    dataset = get_object_or_404(Dataset, id=pk, user=user)
    analysis = dataset.analysis

    try:
        # Get the current page view with all artifacts
        page_view = view_page(analysis, 'analysis')
        
        # Also include dataset card artifact from upload page
        upload_page_view = view_page(analysis, 'upload')
        if upload_page_view.get('artifacts', {}).get('dataset_card'):
            page_view['artifacts']['dataset_card'] = upload_page_view['artifacts']['dataset_card']
        
        available_artifacts = list(page_view.get('artifacts', {}).keys())
        
        # Check if analysis is complete (has all required analysis artifacts)
        required_analysis_artifacts = ['features', 'correlations', 'warnings', 'analysis_summary']
        has_complete_analysis = all(artifact in available_artifacts for artifact in required_analysis_artifacts)
        
        if has_complete_analysis:
            # Analysis is complete, return the data
            return Response({
                'dataset_id': str(dataset.id),
                'analysis_id': str(analysis.id),
                'status': 'complete',
                'message': 'Analysis complete',
                'artifacts': page_view.get('artifacts', {}),
                'available_artifacts': available_artifacts,
                'analysis_data': _convert_artifacts_to_frontend_format(page_view.get('artifacts', {}))
            })
        
        elif 'dataset_card' in available_artifacts:
            # Dataset uploaded but analysis not started, auto-start analysis
            print(f"🔄 Auto-starting analysis for dataset {pk}...")
            
            # Get file path (same logic as dataset_analyze)
            media_url = getattr(settings, 'MEDIA_URL', '/media/')
            media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
            file_url = dataset.file_url or ''
            if file_url.startswith(media_url):
                rel_path = file_url[len(media_url):]
                dataset_path = os.path.join(media_root, rel_path)
            else:
                dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)
            
            # Prepare state for analysis
            state = {
                'dataset_path': dataset_path,
                'dataset_id': str(dataset.id),
                'user_context': request.GET.get('user_context', ''),
                'session_id': str(analysis.id)
            }
            
            # Start analysis (this will run in background)
            try:
                run_page_graph(analysis, 'analysis', state)
                return Response({
                    'dataset_id': str(dataset.id),
                    'analysis_id': str(analysis.id),
                    'status': 'starting',
                    'message': 'Analysis started automatically',
                    'artifacts': page_view.get('artifacts', {}),
                    'available_artifacts': available_artifacts,
                    'analysis_data': None
                })
            except Exception as e:
                print(f"❌ Failed to auto-start analysis: {e}")
                return Response({
                    'dataset_id': str(dataset.id),
                    'analysis_id': str(analysis.id),
                    'status': 'error',
                    'message': f'Failed to start analysis: {str(e)}',
                    'artifacts': page_view.get('artifacts', {}),
                    'available_artifacts': available_artifacts,
                    'analysis_data': None
                })
        else:
            # No dataset uploaded yet
            return Response({
                'dataset_id': str(dataset.id),
                'analysis_id': str(analysis.id),
                'status': 'idle',
                'message': 'No dataset uploaded yet',
                'artifacts': {},
                'available_artifacts': [],
                'analysis_data': None
            })

    except Exception as e:
        return Response({
            'detail': f'Failed to get analysis status: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([AllowAny])
def dataset_target_selection(request, pk):
    """
    Get target selection data for a dataset:
    1. Create mock data with column names, relevance scores and use case descriptions
    2. Load correlation matrix from saved file
    3. Sort by relevance and return to frontend
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )

    try:
        dataset = get_object_or_404(Dataset, pk=pk, user=user)
        
        print(f"🎯 TARGET_SELECTION: Getting data for dataset {pk}")

        # Get file path to load correlation matrix
        media_url = getattr(settings, 'MEDIA_URL', '/media/')
        media_root = getattr(settings, 'MEDIA_ROOT', None) or os.path.join(settings.BASE_DIR, 'images')
        file_url = dataset.file_url or ''
        if file_url.startswith(media_url):
            rel_path = file_url[len(media_url):]
            dataset_path = os.path.join(media_root, rel_path)
        else:
            dataset_path = file_url if os.path.isabs(file_url) else os.path.join(media_root, file_url)

        # Get remaining columns from dataset_card artifact
        remaining_columns = []
        correlation_matrix = {}
        
        try:
            from .models import LangArtifact
            # Get dataset_card artifact to get remaining columns
            dataset_card = LangArtifact.objects.filter(
                analysis=dataset.analysis,
                page='upload',
                kind='dataset_card',
                status='ready'
            ).order_by('-created_at').first()
            
            if dataset_card and dataset_card.payload:
                # Get columns from dataset_card (already has dropped columns applied)
                remaining_columns = [col['name'] for col in dataset_card.payload.get('columns', [])]

            else:
                print(f"No dataset_card artifact found")
            
            # Get mic_pairs (all correlations) - this contains ALL pairs, not just top 5
            mic_pairs_artifact = LangArtifact.objects.filter(
                analysis=dataset.analysis,
                page='analysis',
                kind='mic_pairs',
                status='ready'
            ).order_by('-created_at').first()
            
            if mic_pairs_artifact and mic_pairs_artifact.payload:
                # Extract mic_pairs - stored as {"col1,col2": value, ...}
                mic_pairs = mic_pairs_artifact.payload
                
                # The payload is already in the format {"col1,col2": value}
                # We just need to add reverse mappings for easier lookup
                correlation_matrix = {}
                if isinstance(mic_pairs, dict):
                    for key, mic_value in mic_pairs.items():
                        # Key is in format "col1,col2"
                        parts = key.split(',', 1)
                        if len(parts) == 2:
                            col1, col2 = parts[0], parts[1]
                            correlation_matrix[f"{col1},{col2}"] = float(mic_value)
                            correlation_matrix[f"{col2},{col1}"] = float(mic_value)  # Add reverse mapping
                print(f"📊 TARGET_SELECTION: Loaded {len(correlation_matrix)} correlation pairs from mic_pairs")
        
        except Exception as e:
            print(f"Error getting remaining columns from artifact: {e}")
            # Fallback: load from legacy correlation.json file
            correlation_sidecar = dataset_path + '.correlation.json'
            if os.path.exists(correlation_sidecar):
                with open(correlation_sidecar, 'r') as f:
                    data = json.load(f)
                    correlation_matrix = data.get('correlation_matrix', {})
                    remaining_columns = data.get('remaining_columns', [])

        # Create mock target column data (list of tuples: name, relevance_score, use_case)
        mock_target_data = []
        for i, col in enumerate(remaining_columns):
            # Generate mock relevance score (0.1 to 1.0)
            relevance_score = 0.9 - (i * 0.1) if i < 8 else 0.1 + (i % 3) * 0.1

            # Generate mock use case description
            use_case = generate_use_case_description(col, 'mock')

            mock_target_data.append((col, round(relevance_score, 2), use_case))

        # Sort by relevance score (highest first)
        mock_target_data.sort(key=lambda x: x[1], reverse=True)

        # Convert to format expected by frontend
        target_columns = [
            {
                'name': name,
                'relevance_score': relevance,
                'use_case': use_case
            }
            for name, relevance, use_case in mock_target_data
        ]
        
        print(f"✅ TARGET_SELECTION: Returning {len(target_columns)} target columns, {len(correlation_matrix)} correlations")

        return Response({
            'target_columns': target_columns,
            'correlation_matrix': correlation_matrix,
            'total_columns': len(remaining_columns)
        })

    except Exception as e:
        return Response({
            'detail': f'Target selection data failed: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


def generate_use_case_description(column_name, column_type):
    """Generate a mock use case description for a column based on its name."""
    name_lower = column_name.lower()

    # For mock data, generate descriptions based on column name patterns
    if any(word in name_lower for word in ['price', 'cost', 'amount', 'value', 'revenue', 'sales']):
        return "Predict monetary values or financial outcomes. Useful for regression models to forecast pricing or revenue."
    elif any(word in name_lower for word in ['age', 'year', 'time', 'duration']):
        return "Predict temporal or age-related outcomes. Good for time-series analysis or demographic predictions."
    elif any(word in name_lower for word in ['score', 'rating', 'rank']):
        return "Predict performance metrics or quality ratings. Ideal for recommendation systems or quality assessment."
    elif any(word in name_lower for word in ['category', 'type', 'class', 'group']):
        return "Classify data into distinct categories. Perfect for classification models and segmentation analysis."
    elif any(word in name_lower for word in ['status', 'state', 'condition']):
        return "Predict status or condition outcomes. Useful for binary or multi-class classification problems."
    elif any(word in name_lower for word in ['name', 'id', 'label']):
        return "Identify or label entities. Good for entity recognition and labeling tasks."
    elif any(word in name_lower for word in ['date', 'time']):
        return "Predict temporal patterns or time-based outcomes. Useful for time series forecasting and trend analysis."
    else:
        return "Predict outcomes based on this feature. Suitable for machine learning model target variable."


@api_view(['GET'])
@permission_classes([AllowAny])  # Temporarily allow any access for development
def dashboard_overview(request):
    """
    Get dashboard overview data for the demo user
    """
    # Get or create a default user for development
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={'email': '<EMAIL>', 'first_name': 'Demo', 'last_name': 'User'}
    )

    # Get user's analyses
    analyses = Analysis.objects.filter(user=user, is_active=True)
    analyses_data = AnalysisSerializer(analyses, many=True).data

    return Response({
        'user': {
            'username': user.username,
            'email': user.email,
            'full_name': user.get_full_name()
        },
        'analyses': analyses_data,
    })


def _convert_artifacts_to_frontend_format(artifacts):
    """
    Convert artifacts to the format expected by the frontend.
    Returns None if no meaningful analysis data is available.
    """
    # Convert new artifact format to legacy format for compatibility
    legacy_data = {
        'features': artifacts.get('features', {}).get('payload', []),
        'correlations': artifacts.get('correlations', {}).get('payload', []),
        'warnings': artifacts.get('warnings', {}).get('payload', {}).get('items', []),
        'analysis_summary': artifacts.get('analysis_summary', {}).get('payload', {}),
        'dropped_columns': {
            'user_selected': [],
            'auto_detected': []
        },
        'total_rows': 0,
        'remaining_columns': 0
    }
    
    # Add dropped_columns info from dataset_card if available
    if artifacts.get('dataset_card', {}).get('payload'):
        dataset_card = artifacts['dataset_card']['payload']
        legacy_data['dropped_columns']['user_selected'] = dataset_card.get('dropped_columns', [])
        legacy_data['total_rows'] = dataset_card.get('n_rows', 0)
        legacy_data['remaining_columns'] = dataset_card.get('n_cols', 0)
    
    # Fallback to features data if dataset_card not available
    if legacy_data['features'] and not artifacts.get('dataset_card', {}).get('payload'):
        legacy_data['total_rows'] = legacy_data['features'][0].get('stats', {}).get('count', 0) if legacy_data['features'] else 0
        legacy_data['remaining_columns'] = len(legacy_data['features'])
    
    # Only return data if we have meaningful content (not just dataset_card)
    has_analysis_data = (len(legacy_data['features']) > 0 or 
                        len(legacy_data['correlations']) > 0 or 
                        len(legacy_data['warnings']) > 0 or
                        len(legacy_data['analysis_summary']) > 0)
    
    return legacy_data if has_analysis_data else None
