from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from .choices import PRICING_PLAN_CHOICES


class Customer(models.Model):
    """Customer model for user profile information"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='customer_profile')
    company_name = models.CharField(max_length=255, blank=True, null=True)
    pricing_plan = models.IntegerField("Preistarif", choices=PRICING_PLAN_CHOICES, default=1)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.company_name or 'No Company'}"

    class Meta:
        verbose_name = "Customer"
        verbose_name_plural = "Customers"


class Analysis(models.Model):
    """Analysis model for storing user analysis sessions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='analyses')
    name = models.Char<PERSON><PERSON>(max_length=255)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_edited_at = models.DateTimeField(auto_now=True)
    last_seen_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    current = models.JSONField(default=dict, blank=True, help_text="Current state pointers")

    def __str__(self):
        return f"{self.name} - {self.user.username}"

    class Meta:
        verbose_name = "Analysis"
        verbose_name_plural = "Analyses"
        ordering = ['-last_edited_at', '-last_seen_at']


class Dataset(models.Model):
    """Dataset model for storing dataset information and status"""

    STATUS_CHOICES = [
        ('raw', 'Raw'),
        ('preprocessed', 'Preprocessed'),
        ('embedded', 'Embedded'),
        ('predicted', 'Predicted'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='datasets')
    analysis = models.ForeignKey(Analysis, on_delete=models.CASCADE, related_name='datasets')
    name = models.CharField(max_length=255)
    file_url = models.URLField(max_length=500, help_text="Local URL or S3 URI")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='raw')
    rows_count = models.IntegerField(null=True, blank=True)
    columns_count = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.status}) - {self.analysis.name}"

    class Meta:
        verbose_name = "Dataset"
        verbose_name_plural = "Datasets"
        ordering = ['-created_at']


class LangArtifact(models.Model):
    """Artifact for storing analysis results and intermediate data"""
    STATUS_CHOICES = [
        ('ready', 'Ready'),
        ('stale', 'Stale'),
        ('running', 'Running'),
        ('error', 'Error'),
    ]

    id = models.CharField(primary_key=True, max_length=64, help_text="SHA256 of payload or UUID")
    analysis = models.ForeignKey(Analysis, on_delete=models.CASCADE, related_name='artifacts')
    page = models.CharField(max_length=50, help_text="Page identifier: upload, analysis, etc.")
    kind = models.CharField(max_length=50, help_text="Artifact type: dataset_card, warnings, etc.")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='running')
    depends_on = models.JSONField(default=list, help_text="List of artifact ids this depends on")
    payload = models.JSONField(null=True, blank=True, help_text="Small JSON results")
    uri = models.CharField(max_length=500, null=True, blank=True, help_text="For large objects")
    metrics = models.JSONField(null=True, blank=True, help_text="Timings, sizes, etc.")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.kind} ({self.status}) - {self.analysis.id}"

    class Meta:
        verbose_name = "LangArtifact"
        verbose_name_plural = "LangArtifacts"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['analysis', 'page', 'kind', 'status']),
        ]
