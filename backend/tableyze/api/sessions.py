from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User
import json
import os
from django.conf import settings

from ..models import AnalysisSession, Dataset, Artifact
from ..orchestration.runners import run_page_graph, view_page, resolve_dataset_path
from ..orchestration.tools import save_user_drops

@api_view(['POST'])
@permission_classes([AllowAny])
def create_session(request):
    user, _ = User.objects.get_or_create(username='demo_user', defaults={'email': '<EMAIL>'})
    dataset_id = request.data.get('dataset_id')
    user_context = request.data.get('user_context', '')

    if not dataset_id:
        return Response({'detail': 'dataset_id required'}, status=status.HTTP_400_BAD_REQUEST)

    dataset = get_object_or_404(Dataset, id=dataset_id)

    session, created = AnalysisSession.objects.get_or_create(
        dataset_id=str(dataset.file_url),
        defaults={
            'analysis': dataset.analysis,
            'user_context': user_context,
            'current': {}
        }
    )
    if not created and user_context and user_context != (session.user_context or ''):
        session.user_context = user_context
        session.save(update_fields=['user_context'])

    return Response({'session_id': str(session.id), 'created': created}, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

@api_view(['POST'])
@permission_classes([AllowAny])
def run_page(request, session_id, page):
    session = get_object_or_404(AnalysisSession, id=session_id)
    dataset_path = resolve_dataset_path(session.dataset_id)
    state = {
        'dataset_id': session.dataset_id,
        'dataset_path': dataset_path,
        'user_context': session.user_context or ''
    }
    run_page_graph(session, page, state)
    return Response({'ok': True})

@api_view(['GET'])
@permission_classes([AllowAny])
def view_page_api(request, session_id, page):
    session = get_object_or_404(AnalysisSession, id=session_id)
    data = view_page(session, page)
    return Response(data)

@api_view(['PATCH'])
@permission_classes([AllowAny])
def update_drop_columns(request, session_id):
    """
    Update columns to drop and trigger stale propagation.
    This replaces the old dataset_drop_columns endpoint for LangGraph sessions.
    """
    session = get_object_or_404(AnalysisSession, id=session_id)
    
    try:
        # Get columns to drop from request
        drop_columns = request.data.get('drop_columns', [])
        if isinstance(drop_columns, str):
            drop_columns = json.loads(drop_columns)
        
        # Save to sidecar file (maintain compatibility with existing frontend)
        dataset_path = resolve_dataset_path(session.dataset_id)
        save_user_drops(dataset_path, drop_columns)
        
        # Mark dataset_card artifact as stale (if it exists)
        dataset_card_artifacts = Artifact.objects.filter(
            session=session,
            kind='dataset_card',
            status='ready'
        )
        
        for artifact in dataset_card_artifacts:
            artifact.status = 'stale'
            artifact.save()
        
        # Mark all downstream artifacts as stale
        from ..orchestration.runners import _mark_downstream_stale
        for artifact in dataset_card_artifacts:
            _mark_downstream_stale(session, artifact.id)
        
        # Re-run upload graph to create new dataset_card with dropped columns
        state = {
            'dataset_id': session.dataset_id,
            'dataset_path': dataset_path,
            'user_context': session.user_context or ''
        }
        run_page_graph(session, 'upload', state)
        
        return Response({
            'ok': True,
            'drop_columns': drop_columns,
            'message': 'Columns updated and analysis refreshed'
        })
        
    except Exception as e:
        return Response({
            'detail': f'Failed to update columns: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)