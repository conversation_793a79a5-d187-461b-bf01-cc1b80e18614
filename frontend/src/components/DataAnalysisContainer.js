import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';
import ContainerBox from './ContainerBox';
import './DataAnalysisContainer.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const DataAnalysisContainer = ({ analysis, visible, onAnalysisComplete, onTargetSelectionReady }) => {
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [analysisStatus, setAnalysisStatus] = useState('idle'); // 'idle', 'starting', 'polling', 'complete'

  const isReady = analysisData && !loading;

  useEffect(() => {
    if (visible && analysis?.datasets?.[0]?.id) {
      // Simple API call - backend handles all the logic
      checkAnalysisStatus();
    }
  }, [visible, analysis?.datasets]);

  const startPolling = () => {
    if (!analysis?.datasets?.[0]?.id) return;
    
    const pollInterval = setInterval(async () => {
      try {
        const { apiService } = await import('../services/api');
        const response = await apiService.getAnalysisStatus(analysis.datasets[0].id);
        const status = response.data;
        
        console.log('Polling status:', status.status);
        
        // Backend handles all the logic
        if (status.status === 'complete' && status.analysis_data) {
          // Analysis complete with data
          clearInterval(pollInterval);
          setAnalysisData(status.analysis_data);
          setAnalysisStatus('complete');
          setLoading(false);
          onAnalysisComplete?.(status.analysis_data);
          console.log('Analysis complete!');
          
          // Pass correlation matrix to target selection if available
          // Target selection will fetch its own data, but we need to trigger it to show
          if (status.artifacts) {
            onTargetSelectionReady?.(null); // Signal that analysis is complete
          }
        } else if (status.status === 'error') {
          // Analysis failed
          clearInterval(pollInterval);
          setError(status.message || 'Analysis failed');
          setAnalysisStatus('idle');
          setLoading(false);
        }
        // Continue polling for 'starting' or other in-progress statuses
        
      } catch (err) {
        console.error('Polling error:', err);
        clearInterval(pollInterval);
        setError('Failed to get analysis results. Please try again.');
        setAnalysisStatus('idle');
        setLoading(false);
      }
    }, 2000); // Poll every 2 seconds
    
    // Clear interval after 2 minutes to prevent infinite polling
    setTimeout(() => {
      clearInterval(pollInterval);
      if (analysisStatus === 'polling') {
        setError('Analysis timed out. Please try again.');
        setAnalysisStatus('idle');
        setLoading(false);
      }
    }, 120000); // 2 minutes
  };

  const checkAnalysisStatus = async () => {
    if (!analysis?.datasets?.[0]?.id) return;
    
    try {
      const { apiService } = await import('../services/api');
      const response = await apiService.getAnalysisStatus(analysis.datasets[0].id);
      const status = response.data;
      
      // Backend handles all the logic and returns processed data
      if (status.status === 'complete' && status.analysis_data) {
        // Analysis complete with data
        setAnalysisData(status.analysis_data);
        setAnalysisStatus('complete');
        onAnalysisComplete?.(status.analysis_data);
        
        // Pass correlation matrix to target selection if available
        // Target selection will fetch its own data, but we need to trigger it to show
        if (status.artifacts) {
          onTargetSelectionReady?.(null); // Signal that analysis is complete
        }
      } else if (status.status === 'starting') {
        // Analysis started, begin polling
        setAnalysisStatus('polling');
        setLoading(true);
        startPolling();
      } else if (status.status === 'idle') {
        // Ready to start
        setAnalysisStatus('idle');
      } else {
        // Error or other status
        setError(status.message || 'Unknown status');
        setAnalysisStatus('idle');
      }
    } catch (err) {
      console.error('Failed to check analysis status:', err);
      setError('Failed to check analysis status');
      setAnalysisStatus('idle');
    }
  };

  const createHistogramData = (feature) => {
    if (feature.type === 'numerical') {
      const { counts, bin_edges } = feature.histogram;
      const labels = bin_edges.slice(0, -1).map((edge, i) => 
        `${edge.toFixed(2)}-${bin_edges[i + 1].toFixed(2)}`
      );
      
      return {
        labels,
        datasets: [{
          data: counts,
          backgroundColor: 'rgba(59, 130, 246, 0.6)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
        }]
      };
    } else if (feature.type === 'categorical') {
      const { categories, counts } = feature.histogram;
      return {
        labels: categories,
        datasets: [{
          data: counts,
          backgroundColor: 'rgba(139, 92, 246, 0.6)',
          borderColor: 'rgba(139, 92, 246, 1)',
          borderWidth: 1,
        }]
      };
    }
    return null;
  };

  const createCorrelationGaugeData = (correlation) => {
    const absCorr = Math.abs(correlation.correlation);
    const remaining = 1 - absCorr;
    
    // Color based on correlation strength: red (low) to green (high)
    const red = Math.round(255 * (1 - absCorr));
    const green = Math.round(200 * absCorr); // Reduced from 255 to 200 for less bright green
    const color = `rgb(${red}, ${green}, 0)`;
    
    return {
      labels: ['Correlation', 'Remaining'],
      datasets: [{
        data: [absCorr, remaining],
        backgroundColor: [color, 'rgba(229, 231, 235, 0.3)'],
        borderWidth: 0,
        cutout: '70%',
      }]
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: true }
    },
    scales: {
      x: { display: false },
      y: { display: false }
    }
  };

  const gaugeOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: false }
    }
  };

  if (!visible) return null;

  return (
    <ContainerBox
      title="2. Data Analysis"
      isReady={isReady}
      readyLabel="Target Selection"
      onReadyClick={() => {/* parent can handle next container */}}
    >
      {loading && (
        <div className="analysis-loading">
          <div className="loading-spinner"></div>
          <p>Analyzing your data...</p>
        </div>
      )}

      {error && (
        <div className="analysis-error">
          <p>{error}</p>
          <button className="btn btn-primary btn-sm" onClick={checkAnalysisStatus}>
            Retry Analysis
          </button>
        </div>
      )}

      {analysisData && (
        <div className="analysis-results">
          {/* Features Section */}
          <div className="analysis-section">
            <h3 className="analysis-section__title">Features</h3>
            <div className="features-scroll">
              {analysisData.features.map((feature, index) => {
                const histogramData = createHistogramData(feature);
                return (
                  <div key={index} className="feature-card">
                    <div className="feature-card__header">
                      <span className={`feature-type feature-type--${feature.type}`}>
                        {feature.type}
                      </span>
                      <h4 className="feature-card__name">{feature.name}</h4>
                    </div>
                    
                    <div className="feature-card__chart">
                      {histogramData && (
                        <Bar data={histogramData} options={chartOptions} />
                      )}
                    </div>
                    
                    <div className="feature-card__stats">
                      {feature.type === 'numerical' && (
                        <>
                          <div className="stat-item">
                            <span className="stat-label">Mean:</span>
                            <span className="stat-value">{feature.stats.mean?.toFixed(2)}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Median:</span>
                            <span className="stat-value">{feature.stats.median?.toFixed(2)}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Min:</span>
                            <span className="stat-value">{feature.stats.min?.toFixed(2)}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Max:</span>
                            <span className="stat-value">{feature.stats.max?.toFixed(2)}</span>
                          </div>
                        </>
                      )}
                      
                      {feature.type === 'categorical' && (
                        <>
                          <div className="stat-item">
                            <span className="stat-label">Most Common:</span>
                            <span className="stat-value">{feature.stats.most_common}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Unique Values:</span>
                            <span className="stat-value">{feature.stats.unique_count}</span>
                          </div>
                        </>
                      )}
                      
                      <div className="stat-item">
                        <span className="stat-label">Count:</span>
                        <span className="stat-value">{feature.stats.count}</span>
                      </div>
                      
                      {feature.stats.null_count > 0 && (
                        <div className="stat-item">
                          <span className="stat-label">Missing:</span>
                          <span className="stat-value">{feature.stats.null_count}</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Correlations Section */}
          {analysisData.correlations && analysisData.correlations.length > 0 && (
            <div className="analysis-section">
              <h3 className="analysis-section__title">Correlations</h3>
              <div className="correlations-grid">
                {analysisData.correlations.map((corr, index) => {
                  const gaugeData = createCorrelationGaugeData(corr);
                  return (
                    <div key={index} className="correlation-card">
                      <div className="correlation-card__chart">
                        <Doughnut data={gaugeData} options={gaugeOptions} />
                        <div className="correlation-card__value">
                          {Math.abs(corr.correlation).toFixed(2)}
                        </div>
                      </div>
                      <div className="correlation-card__labels">
                        <div className="correlation-card__feature">{corr.feature1}</div>
                        <div className="correlation-card__vs">vs</div>
                        <div className="correlation-card__feature">{corr.feature2}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Summary */}
          <div className="analysis-summary">
            <p>
              Analyzed {analysisData.total_rows} rows with {analysisData.remaining_columns} features
              {analysisData.dropped_columns && 
               (analysisData.dropped_columns.user_selected?.length || 0) + (analysisData.dropped_columns.auto_detected?.length || 0) > 0 && (
                <span> (dropped {(analysisData.dropped_columns.user_selected?.length || 0) + (analysisData.dropped_columns.auto_detected?.length || 0)} columns: {(analysisData.dropped_columns.auto_detected.concat(analysisData.dropped_columns.user_selected)).join(", ")})</span>
              )}
            </p>
          </div>
        </div>
      )}
    </ContainerBox>
  );
};

export default DataAnalysisContainer;