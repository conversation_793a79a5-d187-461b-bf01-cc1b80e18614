.analysis-loading,
.analysis-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analysis-loading p,
.analysis-error p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.analysis-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.analysis-section__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

/* Features Section */
.features-scroll {
  display: flex;
  gap: var(--spacing-lg);
  overflow-x: auto;
  padding-bottom: var(--spacing-sm);
}

.features-scroll::-webkit-scrollbar {
  height: 6px;
}

.features-scroll::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-sm);
}

.features-scroll::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-sm);
}

.features-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

.feature-card {
  flex: 0 0 280px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.feature-card__header {
  display: flex;
  flex-direction: column;
  /* align-items: center; */
  gap: var(--spacing-sm);
}

.feature-type {
  font-size: var(--font-size-xxs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.feature-type--numerical {
  color: var(--color-primary-dark);
}

.feature-type--categorical {
  color: var(--color-secondary-dark);
}

.feature-type--date {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.feature-card__name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.feature-card__chart {
  height: 120px;
  position: relative;
}

.feature-card__stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.stat-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

/* Correlations Section */
.correlations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: var(--spacing-lg);
}

.correlation-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.correlation-card__chart {
  position: relative;
  width: 80px;
  height: 80px;
}

.correlation-card__value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.correlation-card__labels {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  text-align: center;
}

.correlation-card__feature {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.correlation-card__vs {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  text-transform: uppercase;
}

/* Summary */
.analysis-summary {
  padding: var(--spacing-lg);
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  text-align: center;
}

.analysis-summary p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .features-scroll {
    gap: var(--spacing-md);
  }
  
  .feature-card {
    flex: 0 0 240px;
    padding: var(--spacing-md);
  }
  
  .feature-card__chart {
    height: 100px;
  }
  
  .correlations-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--spacing-md);
  }
  
  .correlation-card {
    padding: var(--spacing-md);
  }
  
  .correlation-card__chart {
    width: 60px;
    height: 60px;
  }
}