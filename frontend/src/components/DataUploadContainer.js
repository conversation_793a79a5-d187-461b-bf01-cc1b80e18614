import React, { useCallback, useMemo, useRef, useState } from 'react';
import ContainerBox from './ContainerBox';
import './DataUploadContainer.css';

const MAX_SIZE_BYTES = 100 * 1024 * 1024; // 100MB
const ACCEPTED_EXTENSIONS = ['.csv', '.xls', '.xlsx'];

function getFileExt(name) {
  const dot = name.lastIndexOf('.');
  return dot >= 0 ? name.slice(dot).toLowerCase() : '';
}

function isAcceptedFile(file) {
  const ext = getFileExt(file.name);
  return ACCEPTED_EXTENSIONS.includes(ext);
}


const DataUploadContainer = ({
  analysis,
  isNewAnalysis,
  ensureAnalysisExists, // async () => { id, ... }
  onDatasetCreated, // (dataset) => void
  onReadyClick, // () => void
}) => {
  const inputRef = useRef(null);
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [preview, setPreview] = useState(null); // { columns: [], rows: [] }
  const [columnsToDrop, setColumnsToDrop] = useState(new Set());
  const [userContext, setUserContext] = useState('');
  const [originalUserContext, setOriginalUserContext] = useState('');

  const isReady = useMemo(() => !!preview && !uploading, [preview, uploading]);

  // Load preview from backend if existing dataset(s)
  React.useEffect(() => {
    const load = async () => {
      try {
        if (!analysis?.datasets || analysis.datasets.length === 0) return;
        const first = analysis.datasets[0];
        if (!first?.id) return;
        const { apiService } = await import('../services/api');
        const [previewResp, dropsResp, userContextResp] = await Promise.all([
          apiService.getDatasetPreview(first.id),
          apiService.getDropColumns(first.id),
          apiService.getUserContext(first.id),
        ]);
        if (previewResp?.data?.preview) setPreview(previewResp.data.preview);
        if (dropsResp?.data?.drop_columns) setColumnsToDrop(new Set(dropsResp.data.drop_columns));
        if (userContextResp?.data?.hasOwnProperty('user_context')) {
          const contextValue = userContextResp.data.user_context || '';
          setUserContext(contextValue);
          setOriginalUserContext(contextValue);
        }
      } catch (e) { /* best-effort */ }
    };
    load();
  }, [analysis?.datasets]);

  const handleBrowseClick = () => inputRef.current?.click();

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };
  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };
  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer?.files?.length) {
      await processFile(e.dataTransfer.files[0]);
    }
  };

  const processFile = useCallback(async (file) => {
    setError(null);
    if (!isAcceptedFile(file)) {
      setError('Unsupported file type. Please upload a .csv, .xls, or .xlsx file.');
      return;
    }
    if (file.size > MAX_SIZE_BYTES) {
      setError('File too large. Please upload a file smaller than 100MB.');
      return;
    }

    setUploading(true);
    try {
      // Create analysis upfront if needed
      let analysisId = analysis?.id;
      if (isNewAnalysis || !analysisId) {
        const created = await ensureAnalysisExists();
        analysisId = created.id;
      }

      // Upload to backend (multipart). Backend will parse and return preview
      const form = new FormData();
      form.append('file', file);
      form.append('name', file.name);
      form.append('user_context', userContext);

      // Defer import to avoid circular
      const { apiService } = await import('../services/api');
      try {
        const resp = await apiService.uploadDataset(analysisId, form);
        if (resp?.data) {
          // expect: { dataset: {...}, preview: { columns: [], rows: [] } }
          if (resp.data.preview) {
            setPreview(resp.data.preview);
            setColumnsToDrop(new Set());
          }
          if (resp.data.dataset) {
            onDatasetCreated?.(resp.data.dataset);
          }
        }
      } catch (err) {
        console.warn('Upload endpoint not ready or failed:', err);
      }
    } catch (err) {
      console.error(err);
      setError('Failed to process file.');
    } finally {
      setUploading(false);
    }
  }, [analysis?.id, isNewAnalysis, ensureAnalysisExists, onDatasetCreated]);

  const handleFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (file) await processFile(file);
    e.target.value = '';
  };

  const toggleDropColumn = async (col) => {
    let nextArr = [];
    setColumnsToDrop((prev) => {
      const next = new Set(prev);
      if (next.has(col)) next.delete(col); else next.add(col);
      nextArr = Array.from(next);
      return next;
    });
    try {
      if (analysis?.datasets && analysis.datasets[0]?.id) {
        const { apiService } = await import('../services/api');
        await apiService.setDropColumns(analysis.datasets[0].id, nextArr);
      }
    } catch (e) { /* ignore */ }
  };

  const saveUserContext = async () => {
    try {
      if (analysis?.datasets && analysis.datasets[0]?.id) {
        const { apiService } = await import('../services/api');
        await apiService.setUserContext(analysis.datasets[0].id, userContext);
        setOriginalUserContext(userContext);
      }
    } catch (e) { /* ignore */ }
  };

  const handleUserContextChange = (e) => {
    const value = e.target.value;
    if (value.length <= 1000) {
      setUserContext(value);
    }
  };

  const hasUserContextChanges = userContext !== originalUserContext;

  return (
    <ContainerBox
      title="1. Data Upload"
      isReady={isReady}
      readyLabel="Data Analysis"
      onReadyClick={onReadyClick}
    >
      <button
        type="button"
        className={`upload-dropzone ${dragActive ? 'is-dragover' : ''}`}
        onClick={handleBrowseClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={inputRef}
          type="file"
          accept={ACCEPTED_EXTENSIONS.join(',')}
          onChange={handleFileChange}
          hidden
        />
        <p className="upload-text">
          Drag and drop your dataset here, or <span className="upload-link">upload</span>
        </p>
        <p className="upload-subtext">Supported formats: .csv, .xls, .xlsx — Max 100MB</p>
      </button>

      {error && <div className="upload-error">{error}</div>}

      {uploading && (
        <div className="uploading">Uploading...</div>
      )}

      {preview && (
        <div className="preview">
          <h3 className="container-box__section-title__h3">Preview</h3>
          <div className="preview-table-wrapper">
            <table className="preview-table">
              <thead>
                <tr>
                  {preview.columns.map((c, idx) => (
                    <th key={idx}>{c}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {preview.rows.map((row, rIdx) => (
                  <tr key={rIdx}>
                    {preview.columns.map((_, cIdx) => (
                      <td key={cIdx}>{row[cIdx] ?? ''}</td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {preview.columns.length > 0 && (
            <div className="drop-columns">
              <div className="drop-columns__title">Select columns to drop</div>
              <div className="drop-columns__list">
                {preview.columns.map((col) => (
                  <label key={col} className="drop-columns__item">
                    <input
                      type="checkbox"
                      checked={columnsToDrop.has(col)}
                      onChange={() => toggleDropColumn(col)}
                    />
                    <span>{col}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
          <br></br>
          <div className='dataset-user-context'>
            <h3 className='container-box__section-title__h3'>Additional context</h3>
            <div className='dataset-user-context-field-description'>Provide additional context about the data, your business or what you would like to do with the dataset.</div>
            <textarea
              id='dataset-user-context-input'
              className='dataset-user-context__textarea'
              value={userContext}
              onChange={handleUserContextChange}
              placeholder='E.g. this is a customer database of a cookie subscription service and I would like to predict churn propensities.'
              maxLength={1000}
            />
            <div className='dataset-user-context__footer'>
              <span className='dataset-user-context__counter'>
                {userContext.length}/1000 characters
              </span>
              <button
                className={`btn ${hasUserContextChanges ? 'btn-primary' : 'btn-disabled'}`}
                onMouseDown={saveUserContext} // Using onMouseDown instead of onClick because onClick would only be triggered if the mouse button is clicked AND released, 
                // which doesnt happen on the button because it slides up with the smaller text field upon clicking outside the text field.
                disabled={!hasUserContextChanges}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </ContainerBox>
  );
};

export default DataUploadContainer;

